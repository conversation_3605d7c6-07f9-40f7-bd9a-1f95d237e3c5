{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "dependencies": {"@johmun/vue-tags-input": "^2.1.0", "@pencilpix/vue2-clock-picker": "^0.1.6", "@stripe/stripe-js": "^1.20.3", "@trevoreyre/autocomplete-vue": "^2.2.0", "@vee-validate/i18n": "^4.5.3", "axios": "^0.19.2", "babel-polyfill": "^6.26.0", "bootstrap": "^4.5.3", "bootstrap-vue": "^2.21.2", "core-js": "^3.19.0", "cross-env": "^7.0.3", "echarts": "^4.9.0", "es6-promise": "^4.2.8", "jspdf": "^2.3.0", "jspdf-autotable": "^3.5.23", "jspdf-html2canvas": "^1.4.9", "laravel-mix": "^6.0.41", "lodash.orderby": "^4.6.0", "mini-css-extract-plugin": "^2.5.3", "mobile-device-detect": "^0.4.3", "moment": "^2.29.1", "moment-locales-webpack-plugin": "^1.2.0", "nprogress": "^0.2.0", "targets-webpack-plugin": "^4.0.0", "vee-validate": "^3.4.5", "vue": "^2.6.12", "vue-barcode": "^1.3.0", "vue-clickaway": "^2.2.2", "vue-cookie": "^1.1.4", "vue-cookies": "^1.7.4", "vue-easy-print": "0.0.8", "vue-echarts": "^5.0.0-beta.0", "vue-excel-xlsx": "^1.2.2", "vue-good-table": "^2.21.11", "vue-html-to-paper": "^1.4.4", "vue-i18n": "^8.26.5", "vue-lazyload": "^1.3.3", "vue-loader": "^15.9.8", "vue-localstorage": "^0.6.2", "vue-meta": "^2.4.0", "vue-mj-daterangepicker": "^0.1.11", "vue-navigation-bar": "^4.1.0", "vue-perfect-scrollbar": "^0.2.1", "vue-router": "^3.5.3", "vue-select": "^3.16.0", "vue-simple-spinner": "^1.2.10", "vue-sweetalert2": "^3.0.8", "vue2-daterange-picker": "^0.6.8", "vue2-editor": "^2.10.3", "vuedraggable": "^2.24.3", "vuejs-datepicker": "^1.6.2", "vuex": "^3.6.0"}, "devDependencies": {"@vue/composition-api": "^1.2.4", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^6.4.1", "eslint-plugin-vue": "^6.2.2", "postcss-loader": "^6.2.1", "resolve-url-loader": "^5.0.0", "sass": "^1.49.7", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.14", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.2", "webpack-i18n-extractor-plugin": "^2.0.7"}}