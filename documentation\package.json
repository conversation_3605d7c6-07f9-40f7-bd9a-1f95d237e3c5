{"name": "gull-vue-documentation", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node node_modules/gulp/bin/gulp.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Ui-Lib", "license": "ISC", "devDependencies": {"babel-core": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "babel-register": "^6.26.0", "browser-sync": "^2.26.12", "del": "^3.0.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^6.1.0", "gulp-babel": "^7.0.0", "gulp-cssnano": "^2.1.3", "gulp-htmlmin": "^4.0.0", "gulp-if": "^2.0.2", "gulp-imagemin": "^4.1.0", "gulp-rename": "^2.0.0", "gulp-sass": "^4.1.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.1", "gulp-uglify-es": "^1.0.4", "gulp-useref": "^3.1.5", "gulp-wait": "0.0.2", "run-sequence": "^2.2.1", "webpack-stream": "^5.2.1"}, "dependencies": {"node-sass": "^4.14.1"}}