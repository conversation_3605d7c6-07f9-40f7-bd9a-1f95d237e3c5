<?php

namespace App\Policies;

use App\Models\Product;
use App\Models\User;
use App\Models\Permission;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\Models\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Product  $product
     * @return mixed
     */
    public function view(User $user)
    {
        $permission = Permission::where('name', 'products_view')->first();
        return $user->hasRole($permission->roles);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\Models\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        $permission = Permission::where('name', 'products_add')->first();
        return $user->hasRole($permission->roles);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Product  $product
     * @return mixed
     */
    public function update(User $user)
    {
        $permission = Permission::where('name', 'products_edit')->first();
        return $user->hasRole($permission->roles);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Product  $product
     * @return mixed
     */
    public function delete(User $user)
    {
        $permission = Permission::where('name', 'products_delete')->first();
        return $user->hasRole($permission->roles);
    }

    public function barcode(User $user)
    {
        $permission = Permission::where('name', 'barcode_view')->first();
        return $user->hasRole($permission->roles);
    }

    public function Stock_Alerts(User $user)
    {
        $permission = Permission::where('name', 'Reports_quantity_alerts')->first();
        return $user->hasRole($permission->roles);
    }

    public function WarehouseStock(User $user)
    {
        $permission = Permission::where('name', 'Warehouse_report')->first();
        return $user->hasRole($permission->roles);
    }

    public function Top_products(User $user)
    {
        $permission = Permission::where('name', 'Top_products')->first();
        return $user->hasRole($permission->roles);
    }

    public function stock_report(User $user)
    {
        $permission = Permission::where('name', 'stock_report')->first();
        return $user->hasRole($permission->roles);
    }

    public function product_report(User $user)
    {
        $permission = Permission::where('name', 'product_report')->first();
        return $user->hasRole($permission->roles);
    }

    public function count_stock(User $user)
    {
        $permission = Permission::where('name', 'count_stock')->first();
        return $user->hasRole($permission->roles);
    }

    
    public function inventory_valuation(User $user)
    {
        $permission = Permission::where('name', 'inventory_valuation')->first();
        return $user->hasRole($permission->roles);
    }

     public function opening_stock_import(User $user)
    {
        $permission = Permission::where('name', 'opening_stock_import')->first();
        return $user->hasRole($permission->roles);
    }
    

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Product  $product
     * @return mixed
     */
    public function restore(User $user, Article $product)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Product  $product
     * @return mixed
     */
    public function forceDelete(User $user, Article $product)
    {
        //
    }
}
