/*!
 * font-awesome-animation - v0.2.0
 * https://github.com/l-lin/font-awesome-animation
 * License: MIT
 */
 @-webkit-keyframes wrench {
    0% {
      -webkit-transform: rotate(-12deg);
      transform: rotate(-12deg);
    }
  
    8% {
      -webkit-transform: rotate(12deg);
      transform: rotate(12deg);
    }
  
    10% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    18% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    20% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    28% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    30% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    38% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    40% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    48% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    50% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    58% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    60% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    68% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    75%, 100% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
  }
  
  @keyframes wrench {
    0% {
      -webkit-transform: rotate(-12deg);
      transform: rotate(-12deg);
    }
  
    8% {
      -webkit-transform: rotate(12deg);
      transform: rotate(12deg);
    }
  
    10% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    18% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    20% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    28% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    30% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    38% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    40% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    48% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    50% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    58% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    60% {
      -webkit-transform: rotate(-24deg);
      transform: rotate(-24deg);
    }
  
    68% {
      -webkit-transform: rotate(24deg);
      transform: rotate(24deg);
    }
  
    75%, 100% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
  }
  
  .faa-wrench.animated,
  .faa-wrench.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-wrench {
    -webkit-animation: wrench 2.5s ease infinite;
    animation: wrench 2.5s ease infinite;
    transform-origin-x: 90%;
    transform-origin-y: 35%;
    transform-origin-z: initial;
  }
  
  .faa-wrench.animated.faa-fast,
  .faa-wrench.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-wrench.faa-fast {
    -webkit-animation: wrench 1.2s ease infinite;
    animation: wrench 1.2s ease infinite;
  }
  
  .faa-wrench.animated.faa-slow,
  .faa-wrench.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-wrench.faa-slow {
    -webkit-animation: wrench 3.7s ease infinite;
    animation: wrench 3.7s ease infinite;
  }
  
  /* BELL */
  
  @-webkit-keyframes ring {
    0% {
      -webkit-transform: rotate(-15deg);
      transform: rotate(-15deg);
    }
  
    2% {
      -webkit-transform: rotate(15deg);
      transform: rotate(15deg);
    }
  
    4% {
      -webkit-transform: rotate(-18deg);
      transform: rotate(-18deg);
    }
  
    6% {
      -webkit-transform: rotate(18deg);
      transform: rotate(18deg);
    }
  
    8% {
      -webkit-transform: rotate(-22deg);
      transform: rotate(-22deg);
    }
  
    10% {
      -webkit-transform: rotate(22deg);
      transform: rotate(22deg);
    }
  
    12% {
      -webkit-transform: rotate(-18deg);
      transform: rotate(-18deg);
    }
  
    14% {
      -webkit-transform: rotate(18deg);
      transform: rotate(18deg);
    }
  
    16% {
      -webkit-transform: rotate(-12deg);
      transform: rotate(-12deg);
    }
  
    18% {
      -webkit-transform: rotate(12deg);
      transform: rotate(12deg);
    }
  
    20%, 100% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
  }
  
  @keyframes ring {
    0% {
      -webkit-transform: rotate(-15deg);
      transform: rotate(-15deg);
    }
  
    2% {
      -webkit-transform: rotate(15deg);
      transform: rotate(15deg);
    }
  
    4% {
      -webkit-transform: rotate(-18deg);
      transform: rotate(-18deg);
    }
  
    6% {
      -webkit-transform: rotate(18deg);
      transform: rotate(18deg);
    }
  
    8% {
      -webkit-transform: rotate(-22deg);
      transform: rotate(-22deg);
    }
  
    10% {
      -webkit-transform: rotate(22deg);
      transform: rotate(22deg);
    }
  
    12% {
      -webkit-transform: rotate(-18deg);
      transform: rotate(-18deg);
    }
  
    14% {
      -webkit-transform: rotate(18deg);
      transform: rotate(18deg);
    }
  
    16% {
      -webkit-transform: rotate(-12deg);
      transform: rotate(-12deg);
    }
  
    18% {
      -webkit-transform: rotate(12deg);
      transform: rotate(12deg);
    }
  
    20%, 100% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
  }
  
  .faa-ring.animated,
  .faa-ring.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-ring {
    -webkit-animation: ring 2s ease infinite;
    animation: ring 2s ease infinite;
    transform-origin-x: 50%;
    transform-origin-y: 0px;
    transform-origin-z: initial;
  }
  
  .faa-ring.animated.faa-fast,
  .faa-ring.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-ring.faa-fast {
    -webkit-animation: ring 1s ease infinite;
    animation: ring 1s ease infinite;
  }
  
  .faa-ring.animated.faa-slow,
  .faa-ring.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-ring.faa-slow {
    -webkit-animation: ring 3s ease infinite;
    animation: ring 3s ease infinite;
  }
  
  /* VERTICAL */
  
  @-webkit-keyframes vertical {
    0% {
      -webkit-transform: translate(0,-3px);
      transform: translate(0,-3px);
    }
  
    4% {
      -webkit-transform: translate(0,3px);
      transform: translate(0,3px);
    }
  
    8% {
      -webkit-transform: translate(0,-3px);
      transform: translate(0,-3px);
    }
  
    12% {
      -webkit-transform: translate(0,3px);
      transform: translate(0,3px);
    }
  
    16% {
      -webkit-transform: translate(0,-3px);
      transform: translate(0,-3px);
    }
  
    20% {
      -webkit-transform: translate(0,3px);
      transform: translate(0,3px);
    }
  
    22%, 100% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  }
  
  @keyframes vertical {
    0% {
      -webkit-transform: translate(0,-3px);
      transform: translate(0,-3px);
    }
  
    4% {
      -webkit-transform: translate(0,3px);
      transform: translate(0,3px);
    }
  
    8% {
      -webkit-transform: translate(0,-3px);
      transform: translate(0,-3px);
    }
  
    12% {
      -webkit-transform: translate(0,3px);
      transform: translate(0,3px);
    }
  
    16% {
      -webkit-transform: translate(0,-3px);
      transform: translate(0,-3px);
    }
  
    20% {
      -webkit-transform: translate(0,3px);
      transform: translate(0,3px);
    }
  
    22%, 100% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  }
  
  .faa-vertical.animated,
  .faa-vertical.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-vertical {
    -webkit-animation: vertical 2s ease infinite;
    animation: vertical 2s ease infinite;
  }
  
  .faa-vertical.animated.faa-fast,
  .faa-vertical.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-vertical.faa-fast {
    -webkit-animation: vertical 1s ease infinite;
    animation: vertical 1s ease infinite;
  }
  
  .faa-vertical.animated.faa-slow,
  .faa-vertical.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-vertical.faa-slow {
    -webkit-animation: vertical 4s ease infinite;
    animation: vertical 4s ease infinite;
  }
  
  /* HORIZONTAL */
  
  @-webkit-keyframes horizontal {
    0% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  
    6% {
      -webkit-transform: translate(5px,0);
      transform: translate(5px,0);
    }
  
    12% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  
    18% {
      -webkit-transform: translate(5px,0);
      transform: translate(5px,0);
    }
  
    24% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  
    30% {
      -webkit-transform: translate(5px,0);
      transform: translate(5px,0);
    }
  
    36%, 100% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  }
  
  @keyframes horizontal {
    0% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  
    6% {
      -webkit-transform: translate(5px,0);
      transform: translate(5px,0);
    }
  
    12% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  
    18% {
      -webkit-transform: translate(5px,0);
      transform: translate(5px,0);
    }
  
    24% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  
    30% {
      -webkit-transform: translate(5px,0);
      transform: translate(5px,0);
    }
  
    36%, 100% {
      -webkit-transform: translate(0,0);
      transform: translate(0,0);
    }
  }
  
  .faa-horizontal.animated,
  .faa-horizontal.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-horizontal {
    -webkit-animation: horizontal 2s ease infinite;
    animation: horizontal 2s ease infinite;
  }
  
  .faa-horizontal.animated.faa-fast,
  .faa-horizontal.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-horizontal.faa-fast {
    -webkit-animation: horizontal 1s ease infinite;
    animation: horizontal 1s ease infinite;
  }
  
  .faa-horizontal.animated.faa-slow,
  .faa-horizontal.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-horizontal.faa-slow {
    -webkit-animation: horizontal 3s ease infinite;
    animation: horizontal 3s ease infinite;
  }
  
  /* FLASHING */
  
  @-webkit-keyframes flash {
    0%, 100%, 50% {
      opacity: 1;
    }
  
    25%, 75% {
      opacity: 0;
    }
  }
  
  @keyframes flash {
    0%, 100%, 50% {
      opacity: 1;
    }
  
    25%, 75% {
      opacity: 0;
    }
  }
  
  .faa-flash.animated,
  .faa-flash.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-flash {
    -webkit-animation: flash 2s ease infinite;
    animation: flash 2s ease infinite;
  }
  
  .faa-flash.animated.faa-fast,
  .faa-flash.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-flash.faa-fast {
    -webkit-animation: flash 1s ease infinite;
    animation: flash 1s ease infinite;
  }
  
  .faa-flash.animated.faa-slow,
  .faa-flash.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-flash.faa-slow {
    -webkit-animation: flash 3s ease infinite;
    animation: flash 3s ease infinite;
  }
  
  /* BOUNCE */
  
  @-webkit-keyframes bounce {
    0%, 10%, 20%, 50%, 80%, 100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
  
    40% {
      -webkit-transform: translateY(-15px);
      transform: translateY(-15px);
    }
  
    60% {
      -webkit-transform: translateY(-15px);
      transform: translateY(-15px);
    }
  }
  
  @keyframes bounce {
    0%, 10%, 20%, 50%, 80%, 100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
  
    40% {
      -webkit-transform: translateY(-15px);
      transform: translateY(-15px);
    }
  
    60% {
      -webkit-transform: translateY(-15px);
      transform: translateY(-15px);
    }
  }
  
  .faa-bounce.animated,
  .faa-bounce.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-bounce {
    -webkit-animation: bounce 2s ease infinite;
    animation: bounce 2s ease infinite;
  }
  
  .faa-bounce.animated.faa-fast,
  .faa-bounce.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-bounce.faa-fast {
    -webkit-animation: bounce 1s ease infinite;
    animation: bounce 1s ease infinite;
  }
  
  .faa-bounce.animated.faa-slow,
  .faa-bounce.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-bounce.faa-slow {
    -webkit-animation: bounce 3s ease infinite;
    animation: bounce 3s ease infinite;
  }
  
  /* SPIN */
  
  @-webkit-keyframes spin {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
  
    100% {
      -webkit-transform: rotate(359deg);
      transform: rotate(359deg);
    }
  }
  
  @keyframes spin {
    0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg);
    }
  
    100% {
      -webkit-transform: rotate(359deg);
      transform: rotate(359deg);
    }
  }
  
  .faa-spin.animated,
  .faa-spin.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-spin {
    -webkit-animation: spin 1.5s linear infinite;
    animation: spin 1.5s linear infinite;
  }
  
  .faa-spin.animated.faa-fast,
  .faa-spin.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-spin.faa-fast {
    -webkit-animation: spin 0.7s linear infinite;
    animation: spin 0.7s linear infinite;
  }
  
  .faa-spin.animated.faa-slow,
  .faa-spin.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-spin.faa-slow {
    -webkit-animation: spin 2.2s linear infinite;
    animation: spin 2.2s linear infinite;
  }
  
  /* FLOAT */
  
  @-webkit-keyframes float {
    0% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
  
    50% {
      -webkit-transform: translateY(-6px);
      transform: translateY(-6px);
    }
  
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
  }
  
  @keyframes float {
    0% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
  
    50% {
      -webkit-transform: translateY(-6px);
      transform: translateY(-6px);
    }
  
    100% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
  }
  
  .faa-float.animated,
  .faa-float.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-float {
    -webkit-animation: float 2s linear infinite;
    animation: float 2s linear infinite;
  }
  
  .faa-float.animated.faa-fast,
  .faa-float.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-float.faa-fast {
    -webkit-animation: float 1s linear infinite;
    animation: float 1s linear infinite;
  }
  
  .faa-float.animated.faa-slow,
  .faa-float.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-float.faa-slow {
    -webkit-animation: float 3s linear infinite;
    animation: float 3s linear infinite;
  }
  
  /* PULSE */
  
  @-webkit-keyframes pulse {
    0% {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  
    50% {
      -webkit-transform: scale(0.8);
      transform: scale(0.8);
    }
  
    100% {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  
  @keyframes pulse {
    0% {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  
    50% {
      -webkit-transform: scale(0.8);
      transform: scale(0.8);
    }
  
    100% {
      -webkit-transform: scale(1.1);
      transform: scale(1.1);
    }
  }
  
  .faa-pulse.animated,
  .faa-pulse.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-pulse {
    -webkit-animation: pulse 2s linear infinite;
    animation: pulse 2s linear infinite;
  }
  
  .faa-pulse.animated.faa-fast,
  .faa-pulse.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-pulse.faa-fast {
    -webkit-animation: pulse 1s linear infinite;
    animation: pulse 1s linear infinite;
  }
  
  .faa-pulse.animated.faa-slow,
  .faa-pulse.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-pulse.faa-slow {
    -webkit-animation: pulse 3s linear infinite;
    animation: pulse 3s linear infinite;
  }
  
  /* SHAKE */
  
  .faa-shake.animated,
  .faa-shake.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-shake {
    -webkit-animation: wrench 2.5s ease infinite;
    animation: wrench 2.5s ease infinite;
  }
  
  .faa-shake.animated.faa-fast,
  .faa-shake.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-shake.faa-fast {
    -webkit-animation: wrench 1.2s ease infinite;
    animation: wrench 1.2s ease infinite;
  }
  
  .faa-shake.animated.faa-slow,
  .faa-shake.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-shake.faa-slow {
    -webkit-animation: wrench 3.7s ease infinite;
    animation: wrench 3.7s ease infinite;
  }
  
  /* TADA */
  
  @-webkit-keyframes tada {
    0% {
      -webkit-transform: scale(1);
      transform: scale(1);
    }
  
    10%, 20% {
      -webkit-transform: scale(.9) rotate(-8deg);
      transform: scale(.9) rotate(-8deg);
    }
  
    30%, 50%, 70% {
      -webkit-transform: scale(1.3) rotate(8deg);
      transform: scale(1.3) rotate(8deg);
    }
  
    40%, 60% {
      -webkit-transform: scale(1.3) rotate(-8deg);
      transform: scale(1.3) rotate(-8deg);
    }
  
    80%, 100% {
      -webkit-transform: scale(1) rotate(0);
      transform: scale(1) rotate(0);
    }
  }
  
  @keyframes tada {
    0% {
      -webkit-transform: scale(1);
      transform: scale(1);
    }
  
    10%, 20% {
      -webkit-transform: scale(.9) rotate(-8deg);
      transform: scale(.9) rotate(-8deg);
    }
  
    30%, 50%, 70% {
      -webkit-transform: scale(1.3) rotate(8deg);
      transform: scale(1.3) rotate(8deg);
    }
  
    40%, 60% {
      -webkit-transform: scale(1.3) rotate(-8deg);
      transform: scale(1.3) rotate(-8deg);
    }
  
    80%, 100% {
      -webkit-transform: scale(1) rotate(0);
      transform: scale(1) rotate(0);
    }
  }
  
  .faa-tada.animated,
  .faa-tada.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-tada {
    -webkit-animation: tada 2s linear infinite;
    animation: tada 2s linear infinite;
  }
  
  .faa-tada.animated.faa-fast,
  .faa-tada.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-tada.faa-fast {
    -webkit-animation: tada 1s linear infinite;
    animation: tada 1s linear infinite;
  }
  
  .faa-tada.animated.faa-slow,
  .faa-tada.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-tada.faa-slow {
    -webkit-animation: tada 3s linear infinite;
    animation: tada 3s linear infinite;
  }
  
  /* PASSING */
  
  @-webkit-keyframes passing {
    0% {
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      opacity: 0;
    }
  
    50% {
      -webkit-transform: translateX(0%);
      transform: translateX(0%);
      opacity: 1;
    }
  
    100% {
      -webkit-transform: translateX(50%);
      transform: translateX(50%);
      opacity: 0;
    }
  }
  
  @keyframes passing {
    0% {
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      opacity: 0;
    }
  
    50% {
      -webkit-transform: translateX(0%);
      transform: translateX(0%);
      opacity: 1;
    }
  
    100% {
      -webkit-transform: translateX(50%);
      transform: translateX(50%);
      opacity: 0;
    }
  }
  
  .faa-passing.animated,
  .faa-passing.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-passing {
    -webkit-animation: passing 2s linear infinite;
    animation: passing 2s linear infinite;
  }
  
  .faa-passing.animated.faa-fast,
  .faa-passing.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-passing.faa-fast {
    -webkit-animation: passing 1s linear infinite;
    animation: passing 1s linear infinite;
  }
  
  .faa-passing.animated.faa-slow,
  .faa-passing.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-passing.faa-slow {
    -webkit-animation: passing 3s linear infinite;
    animation: passing 3s linear infinite;
  }
  
  /* PASSING REVERSE */
  
  @-webkit-keyframes passing-reverse {
    0% {
      -webkit-transform: translateX(50%);
      transform: translateX(50%);
      opacity: 0;
    }
  
    50% {
      -webkit-transform: translateX(0%);
      transform: translateX(0%);
      opacity: 1;
    }
  
    100% {
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      opacity: 0;
    }
  }
  
  @keyframes passing-reverse {
    0% {
      -webkit-transform: translateX(50%);
      transform: translateX(50%);
      opacity: 0;
    }
  
    50% {
      -webkit-transform: translateX(0%);
      transform: translateX(0%);
      opacity: 1;
    }
  
    100% {
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      opacity: 0;
    }
  }
  
  .faa-passing-reverse.animated,
  .faa-passing-reverse.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-passing-reverse {
    -webkit-animation: passing-reverse 2s linear infinite;
    animation: passing-reverse 2s linear infinite;
  }
  
  .faa-passing-reverse.animated.faa-fast,
  .faa-passing-reverse.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-passing-reverse.faa-fast {
    -webkit-animation: passing-reverse 1s linear infinite;
    animation: passing-reverse 1s linear infinite;
  }
  
  .faa-passing-reverse.animated.faa-slow,
  .faa-passing-reverse.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-passing-reverse.faa-slow {
    -webkit-animation: passing-reverse 3s linear infinite;
    animation: passing-reverse 3s linear infinite;
  }
  
  /* BURST */
  
  @-webkit-keyframes burst {
    0% {
      opacity: .6;
    }
  
    50% {
      -webkit-transform: scale(1.8);
      transform: scale(1.8);
      opacity: 0;
    }
  
    100% {
      opacity: 0;
    }
  }
  
  @keyframes burst {
    0% {
      opacity: .6;
    }
  
    50% {
      -webkit-transform: scale(1.8);
      transform: scale(1.8);
      opacity: 0;
    }
  
    100% {
      opacity: 0;
    }
  }
  
  .faa-burst.animated,
  .faa-burst.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-burst {
    -webkit-animation: burst 2s infinite linear;
    animation: burst 2s infinite linear;
  }
  
  .faa-burst.animated.faa-fast,
  .faa-burst.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-burst.faa-fast {
    -webkit-animation: burst 1s infinite linear;
    animation: burst 1s infinite linear;
  }
  
  .faa-burst.animated.faa-slow,
  .faa-burst.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-burst.faa-slow {
    -webkit-animation: burst 3s infinite linear;
    animation: burst 3s infinite linear;
  }
  
  /* FALLING */
  
  @-webkit-keyframes falling {
    0% {
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      opacity: 0;
    }
  
    50% {
      -webkit-transform: translateY(0%);
      transform: translateY(0%);
      opacity: 1;
    }
  
    100% {
      -webkit-transform: translateY(50%);
      transform: translateY(50%);
      opacity: 0;
    }
  }
  
  @keyframes falling {
    0% {
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      opacity: 0;
    }
  
    50% {
      -webkit-transform: translateY(0%);
      transform: translateY(0%);
      opacity: 1;
    }
  
    100% {
      -webkit-transform: translateY(50%);
      transform: translateY(50%);
      opacity: 0;
    }
  }
  
  .faa-falling.animated,
  .faa-falling.animated-hover:hover,
  .faa-parent.animated-hover:hover > .faa-falling {
    -webkit-animation: falling 2s linear infinite;
    animation: falling 2s linear infinite;
  }
  
  .faa-falling.animated.faa-fast,
  .faa-falling.animated-hover.faa-fast:hover,
  .faa-parent.animated-hover:hover > .faa-falling.faa-fast {
    -webkit-animation: falling 1s linear infinite;
    animation: falling 1s linear infinite;
  }
  
  .faa-falling.animated.faa-slow,
  .faa-falling.animated-hover.faa-slow:hover,
  .faa-parent.animated-hover:hover > .faa-falling.faa-slow {
    -webkit-animation: falling 3s linear infinite;
    animation: falling 3s linear infinite;
  }
  
  
  /*# sourceMappingURL=data:application/json;base64,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 */