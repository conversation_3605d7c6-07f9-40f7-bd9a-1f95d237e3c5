{"name": "autopost-master", "version": "1.0.0", "description": "Application web d'automatisation de contenu pour les réseaux sociaux", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["social-media", "automation", "content-generation", "instagram", "facebook", "ai"], "author": "AutoPost Master", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "axios": "^1.6.0", "node-cron": "^3.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.1"}}