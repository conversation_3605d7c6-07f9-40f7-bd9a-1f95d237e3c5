/*
 * Container style
 */
.ps {
  overflow: hidden !important;
  overflow-anchor: none;
  -ms-overflow-style: none;
  touch-action: auto;
  -ms-touch-action: auto;
}

/*
 * Scrollbar rail styles
 */
.ps__rail-x {
  display: none;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  height: 15px;
  /* there must be 'bottom' or 'top' for ps__rail-x */
  bottom: 0px;
  /* please don't change 'position' */
  position: absolute;
}

.ps__rail-y {
  display: none;
  opacity: 0;
  transition: background-color 0.2s linear, opacity 0.2s linear;
  -webkit-transition: background-color 0.2s linear, opacity 0.2s linear;
  width: 15px;
  /* there must be 'right' or 'left' for ps__rail-y */
  right: 0;
  /* please don't change 'position' */
  position: absolute;
}

.ps--active-x > .ps__rail-x,
.ps--active-y > .ps__rail-y {
  display: block;
  background-color: transparent;
}

.ps:hover > .ps__rail-x,
.ps:hover > .ps__rail-y,
.ps--focus > .ps__rail-x,
.ps--focus > .ps__rail-y,
.ps--scrolling-x > .ps__rail-x,
.ps--scrolling-y > .ps__rail-y {
  opacity: 0.6;
}

.ps .ps__rail-x:hover,
.ps .ps__rail-y:hover,
.ps .ps__rail-x:focus,
.ps .ps__rail-y:focus,
.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-y.ps--clicking {
  background-color: #eee;
  opacity: 0.9;
}

/*
 * Scrollbar thumb styles
 */
.ps__thumb-x {
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color 0.2s linear, height 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s linear, height 0.2s ease-in-out;
  height: 6px;
  /* there must be 'bottom' for ps__thumb-x */
  bottom: 2px;
  /* please don't change 'position' */
  position: absolute;
}

.ps__thumb-y {
  background-color: #aaa;
  border-radius: 6px;
  transition: background-color 0.2s linear, width 0.2s ease-in-out;
  -webkit-transition: background-color 0.2s linear, width 0.2s ease-in-out;
  width: 6px;
  /* there must be 'right' for ps__thumb-y */
  right: 2px;
  /* please don't change 'position' */
  position: absolute;
}

.ps__rail-x:hover > .ps__thumb-x,
.ps__rail-x:focus > .ps__thumb-x,
.ps__rail-x.ps--clicking .ps__thumb-x {
  background-color: #999;
  height: 11px;
}

.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: #999;
  width: 11px;
}

/* MS supports */
@supports (-ms-overflow-style: none) {
  .ps {
    overflow: auto !important;
  }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .ps {
    overflow: auto !important;
  }
}
.tns-outer {
  padding: 0 !important;
}

.tns-outer [hidden] {
  display: none !important;
}

.tns-outer [aria-controls], .tns-outer [data-action] {
  cursor: pointer;
}

.tns-slider {
  -webkit-transition: all 0s;
  -moz-transition: all 0s;
  transition: all 0s;
}

.tns-slider > .tns-item {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.tns-horizontal.tns-subpixel {
  white-space: nowrap;
}

.tns-horizontal.tns-subpixel > .tns-item {
  display: inline-block;
  vertical-align: top;
  white-space: normal;
}

.tns-horizontal.tns-no-subpixel:after {
  content: "";
  display: table;
  clear: both;
}

.tns-horizontal.tns-no-subpixel > .tns-item {
  float: left;
}

.tns-horizontal.tns-carousel.tns-no-subpixel > .tns-item {
  margin-right: -100%;
}

.tns-no-calc {
  position: relative;
  left: 0;
}

.tns-gallery {
  position: relative;
  left: 0;
  min-height: 1px;
}

.tns-gallery > .tns-item {
  position: absolute;
  left: -100%;
  -webkit-transition: transform 0s, opacity 0s;
  -moz-transition: transform 0s, opacity 0s;
  transition: transform 0s, opacity 0s;
}

.tns-gallery > .tns-slide-active {
  position: relative;
  left: auto !important;
}

.tns-gallery > .tns-moving {
  -webkit-transition: all 0.25s;
  -moz-transition: all 0.25s;
  transition: all 0.25s;
}

.tns-autowidth {
  display: inline-block;
}

.tns-lazy-img {
  -webkit-transition: opacity 0.6s;
  -moz-transition: opacity 0.6s;
  transition: opacity 0.6s;
  opacity: 0.6;
}

.tns-lazy-img.tns-complete {
  opacity: 1;
}

.tns-ah {
  -webkit-transition: height 0s;
  -moz-transition: height 0s;
  transition: height 0s;
}

.tns-ovh {
  overflow: hidden;
}

.tns-visually-hidden {
  position: absolute;
  left: -10000em;
}

.tns-transparent {
  opacity: 0;
  visibility: hidden;
}

.tns-fadeIn {
  opacity: 1;
  filter: alpha(opacity=100);
  z-index: 0;
}

.tns-normal, .tns-fadeOut {
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: -1;
}

.tns-vpfix {
  white-space: nowrap;
}

.tns-vpfix > div, .tns-vpfix > li {
  display: inline-block;
}

.tns-t-subp2 {
  margin: 0 auto;
  width: 310px;
  position: relative;
  height: 10px;
  overflow: hidden;
}

.tns-t-ct {
  width: 2333.3333333%;
  width: -webkit-calc(100% * 70 / 3);
  width: -moz-calc(100% * 70 / 3);
  width: 2333.3333333333%;
  position: absolute;
  right: 0;
}

.tns-t-ct:after {
  content: "";
  display: table;
  clear: both;
}

.tns-t-ct > div {
  width: 1.4285714%;
  width: -webkit-calc(100% / 70);
  width: -moz-calc(100% / 70);
  width: 1.4285714286%;
  height: 10px;
  float: left;
}

/*# sourceMappingURL=sourcemaps/tiny-slider.css.map */
:root {
  --gl-star-color: #fdd835;
  --gl-star-color-inactive: #dcdce6;
  --gl-star-empty: url("../img/star-empty.svg");
  --gl-star-full: url("../img/star-full.svg");
  --gl-star-size: 24px;
  --gl-tooltip-border-radius: 4px;
  --gl-tooltip-font-size: 0.875rem;
  --gl-tooltip-font-weight: 400;
  --gl-tooltip-line-height: 1;
  --gl-tooltip-margin: 12px;
  --gl-tooltip-padding: .5em 1em;
  --gl-tooltip-size: 6px;
}

[data-star-rating] > select {
  -webkit-clip-path: circle(1px at 0 0) !important;
  clip-path: circle(1px at 0 0) !important;
  clip: rect(1px, 1px, 1px, 1px) !important;
  height: 1px !important;
  margin: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  top: 0 !important;
  visibility: visible !important;
  white-space: nowrap !important;
  width: 1px !important;
}

[data-star-rating] > select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  pointer-events: none;
}

[data-star-rating] > select::before,
[data-star-rating] > select::after {
  display: none !important;
}

[data-star-rating].gl-star-rating--ltr > select {
  left: 0 !important;
}

[data-star-rating].gl-star-rating--rtl > select {
  right: 0 !important;
}

[data-star-rating] {
  align-items: center;
  display: flex;
  position: relative;
}

.gl-star-rating:not([data-star-rating]) .gl-star-rating--stars {
  display: none;
}

[data-star-rating] .gl-star-rating--stars {
  align-items: center;
  cursor: pointer;
  display: flex;
  position: relative;
}

[data-star-rating] > select:focus + .gl-star-rating--stars span:first-child::before {
  box-shadow: 0 0 0 3px -moz-mac-focusring;
  box-shadow: 0 0 0 3px -webkit-focus-ring-color;
  box-shadow: 0 0 0 3px Highlight;
  content: "";
  display: block;
  height: 100%;
  outline: 1px solid transparent;
  pointer-events: none;
  position: absolute;
  width: 100%;
}

[data-star-rating] select[disabled] + .gl-star-rating--stars {
  cursor: default;
}

[data-star-rating] .gl-star-rating--stars > span {
  display: flex;
  height: 24px;
  height: 24px;
  height: var(--gl-star-size);
  margin: 0;
  width: 24px;
  width: 24px;
  width: var(--gl-star-size);
}

[data-star-rating] .gl-star-rating--stars[aria-label]::before,
[data-star-rating] .gl-star-rating--stars[aria-label]::after {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  bottom: auto;
  box-sizing: border-box;
  left: 100%;
  pointer-events: none;
  position: absolute;
  top: 50%;
  opacity: 0.9;
  transform-origin: top;
  transform: translate3d(0, -50%, 0);
  white-space: nowrap;
  z-index: 10;
}

[data-star-rating] .gl-star-rating--stars[aria-label]::before {
  background-size: 100% auto !important;
  background-position: 50% !important;
}

[data-star-rating] .gl-star-rating--stars[aria-label]::before {
  background: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 31"%3E%3Cpath fill="%23111" d="M12.002 31C12.002 25 0 19.838 0 15.5 0 11.24 12 6 12 0l.002 31z"/%3E%3C/svg%3E') no-repeat;
  content: "";
  height: 18px;
  margin-bottom: 0;
  margin-left: 6px;
  margin-left: 6px;
  margin-left: var(--gl-tooltip-size);
  width: 6px;
  width: 6px;
  width: var(--gl-tooltip-size);
}

[data-star-rating] .gl-star-rating--stars[aria-label]::after {
  background: #111;
  border-radius: 4px;
  border-radius: 4px;
  border-radius: var(--gl-tooltip-border-radius);
  color: #fff;
  content: attr(aria-label);
  font-size: 0.875rem;
  font-size: 0.875rem;
  font-size: var(--gl-tooltip-font-size);
  font-weight: normal;
  margin-left: 12px;
  margin-left: 12px;
  margin-left: var(--gl-tooltip-margin);
  padding: 0.5em 1em;
  padding: 0.5em 1em;
  padding: var(--gl-tooltip-padding);
  text-transform: none;
}

[data-star-rating].gl-star-rating--rtl .gl-star-rating--stars[aria-label]::before,
[data-star-rating].gl-star-rating--rtl .gl-star-rating--stars[aria-label]::after {
  left: auto;
  right: 100%;
}

[data-star-rating].gl-star-rating--rtl .gl-star-rating--stars[aria-label]::before {
  transform: scaleX(-1) translate3d(0, -50%, 0);
  margin-left: 0;
  margin-right: 6px;
  margin-right: 6px;
  margin-right: var(--gl-tooltip-size);
}

[data-star-rating].gl-star-rating--rtl .gl-star-rating--stars[aria-label]::after {
  margin-left: 0;
  margin-right: 12px;
  margin-right: 12px;
  margin-right: var(--gl-tooltip-margin);
}

[data-star-rating] svg {
  height: 100%;
  width: 100%;
}

[data-star-rating] .gl-star-half {
  fill: none;
  stroke: none;
}

[data-star-rating] .gl-star-full {
  fill: #dcdce6;
  fill: #dcdce6;
  fill: var(--gl-star-color-inactive);
  stroke: #dcdce6;
  stroke: #dcdce6;
  stroke: var(--gl-star-color-inactive);
  transition: fill 0.15s ease-in-out, stroke 0.15s ease-in-out;
}

[data-star-rating] .gl-active .gl-star-full {
  fill: #fdd835;
  fill: #fdd835;
  fill: var(--gl-star-color);
  stroke: #fdd835;
  stroke: #fdd835;
  stroke: var(--gl-star-color);
}

/* Compatibilty with v3 */
.gl-star-rating--stars[class*=" s"] > span {
  background-image: url("../img/star-empty.svg") !important;
  background-image: url("../img/star-empty.svg") !important;
  background-image: var(--gl-star-empty) !important;
}

.gl-star-rating--stars[class*=" s"] > span {
  background-position: center;
  background-repeat: no-repeat;
  background-size: 90%;
}

.gl-star-rating--stars[class*=" s"] > span.gl-active,
.gl-star-rating--stars[class*=" s"] > span.gl-active.gl-selected {
  background-image: url("../img/star-full.svg") !important;
  background-image: url("../img/star-full.svg") !important;
  background-image: var(--gl-star-full) !important;
}
.tree-leaf {
  position: relative;
}

.tree-leaf .tree-child-leaves {
  display: block;
  margin-left: 15px;
}

.tree-leaf .hidden {
  display: none;
  visibility: hidden;
}

.tree-leaf .tree-expando {
  background: #ddd;
  border-radius: 3px;
  cursor: pointer;
  float: left;
  height: 10px;
  line-height: 10px;
  position: relative;
  text-align: center;
  top: 5px;
  width: 10px;
}

.tree-leaf .tree-expando:hover {
  background: #aaa;
}

.tree-leaf .tree-leaf-text {
  cursor: pointer;
  float: left;
  margin-left: 5px;
}

.tree-leaf .tree-leaf-text:hover {
  color: #00f;
}

.tree-leaf .tree-leaf-content:after, .tree-leaf .tree-leaf-content:before {
  content: " ";
  display: table;
}

.tree-leaf .tree-leaf-content:after {
  clear: both;
}

/*# sourceMappingURL=treeview.min.css.map */
/*
 Rangeable
 Copyright (c) 2018 Karl Saunders
 Dual licensed under the MIT (http://www.opensource.org/licenses/mit-license.php)
 and GPL (http://www.opensource.org/licenses/gpl-license.php) licenses.

 Version: 0.1.6

*/
.rangeable-container.combined-tooltip.dragging .rangeable-progress > .rangeable-tooltip, .rangeable-container.combined-tooltip.rangeable-tooltips--visible .rangeable-progress > .rangeable-tooltip, .rangeable-container.dragging.rangeable-tooltips .rangeable-handle .rangeable-tooltip, .rangeable-container.rangeable-tooltips--visible.rangeable-tooltips .rangeable-handle .rangeable-tooltip, .rangeable-container.rangeable-vertical.combined-tooltip .rangeable-progress > .rangeable-tooltip::before {
  display: block;
}

.rangeable-container {
  cursor: pointer;
  width: 100%;
}

.rangeable-container.rangeable-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.rangeable-container.rangeable-multiple.combined-tooltip .rangeable-handle .rangeable-tooltip, .rangeable-container.rangeable-vertical.combined-tooltip .rangeable-handle .rangeable-tooltip {
  opacity: 0;
}

.rangeable-container.focus .rangeable-handle {
  border: 1px solid #74b9ff;
}

.rangeable-container.rangeable-multiple .rangeable-handle:nth-child(1) {
  left: 0;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
}

.rangeable-container.rangeable-multiple .rangeable-handle:nth-child(2) {
  right: 0;
}

.rangeable-container.rangeable-vertical {
  height: 100%;
  width: auto;
}

.rangeable-container.rangeable-vertical .rangeable-track {
  width: 8px;
  height: 100%;
}

.rangeable-container.rangeable-vertical .rangeable-progress {
  width: 8px;
  height: 100%;
  top: auto;
  bottom: 0;
  -webkit-transform-origin: 0 100% 0;
  transform-origin: 0 100% 0;
}

.rangeable-container.rangeable-vertical .rangeable-handle {
  right: auto;
  left: 50%;
  top: 0;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
}

.rangeable-container.rangeable-vertical .rangeable-tooltip {
  position: absolute;
  top: 50%;
  left: calc(100% + 6px + 4px + 5px);
  right: auto;
  bottom: auto;
  -webkit-transform: translate3d(0, -50%, 0);
  transform: translate3d(0, -50%, 0);
}

.rangeable-container.rangeable-vertical .rangeable-tooltip::before {
  right: 100%;
  left: auto;
  top: 50%;
  -webkit-transform: translate3d(0, -50%, 0);
  transform: translate3d(0, -50%, 0);
  border-width: 4px 4px 4px 0;
  border-color: transparent #3db13d transparent transparent;
}

.rangeable-container.rangeable-vertical .rangeable-buffer {
  width: 100%;
  height: 0;
}

.rangeable-container.rangeable-vertical.rangeable-multiple .rangeable-handle:nth-child(1) {
  top: 0;
  left: 50%;
}

.rangeable-container.rangeable-vertical.rangeable-multiple .rangeable-handle:nth-child(2) {
  bottom: 0;
  top: auto;
  -webkit-transform: translate3d(-50%, 50%, 0);
  transform: translate3d(-50%, 50%, 0);
}

.rangeable-input {
  position: absolute;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

.rangeable-input:focus + .rangeable-track .rangeable-handle::after {
  position: absolute;
  width: 22px;
  height: 22px;
  bottom: -6px;
  right: -6px;
  outline: #000 dotted 1px;
  content: "";
}

.rangeable-progress, .rangeable-track {
  height: 8px;
  width: 100%;
  border-radius: 4px;
}

.rangeable-track {
  background-color: #ccc;
  position: relative;
}

.rangeable-progress {
  background-color: #3db13d;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-transform-origin: 0 0 0;
  transform-origin: 0 0 0;
}

.rangeable-progress > .rangeable-tooltip {
  display: none;
  z-index: 11;
  top: auto;
  bottom: calc(100% + 7px + 9px);
  white-space: nowrap;
}

.rangeable-handle {
  box-sizing: border-box;
  width: 22px;
  height: 22px;
  border: 6px solid #3db13d;
  border-radius: 50%;
  background-color: #fff;
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translate3d(50%, -50%, 0);
  transform: translate3d(50%, -50%, 0);
}

.rangeable-handle:focus {
  outline: 0;
}

.rangeable-handle:focus::after {
  position: absolute;
  width: 22px;
  height: 22px;
  bottom: -6px;
  right: -6px;
  outline: #000 dotted 1px;
  content: "";
}

.rangeable-handle.active {
  z-index: 10;
}

.rangeable-handle .rangeable-tooltip {
  display: none;
}

.rangeable-tooltip {
  position: absolute;
  right: 50%;
  bottom: calc(100% + 6px + 4px + 5px);
  -webkit-transform: translate3d(50%, 0, 0);
  transform: translate3d(50%, 0, 0);
  text-align: center;
  padding: 2px 13px;
  background-color: #3db13d;
  border-radius: 4px;
  font-weight: 700;
  font-size: 16px;
  color: #fff;
  font-family: Inconsolata, Consolas, Courier New, Lucida Console, sans-serif;
}

.rangeable-tooltip::before {
  width: 0;
  height: 0;
  border-width: 4px 4px 0;
  border-style: solid;
  border-color: #3db13d transparent transparent;
  position: absolute;
  left: 50%;
  top: 100%;
  -webkit-transform: translate3d(-50%, 0, 0);
  transform: translate3d(-50%, 0, 0);
  content: "";
}

.rangeable-buffers {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.rangeable-buffer {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  height: 100%;
}
@keyframes opaque {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes resizeanim {
  0%, to {
    opacity: 0;
  }
}
.apexcharts-canvas {
  position: relative;
  user-select: none;
}

.apexcharts-canvas ::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 6px;
}

.apexcharts-canvas ::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.apexcharts-inner {
  position: relative;
}

.apexcharts-text tspan {
  font-family: inherit;
}

.legend-mouseover-inactive {
  transition: 0.15s ease all;
  opacity: 0.2;
}

.apexcharts-legend-text {
  padding-left: 15px;
  margin-left: -15px;
}

.apexcharts-series-collapsed {
  opacity: 0;
}

.apexcharts-tooltip {
  border-radius: 5px;
  box-shadow: 2px 2px 6px -4px #999;
  cursor: default;
  font-size: 14px;
  left: 62px;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  top: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  white-space: nowrap;
  z-index: 12;
  transition: 0.15s ease all;
}

.apexcharts-tooltip.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-tooltip.apexcharts-theme-light {
  border: 1px solid #e3e3e3;
  background: rgba(255, 255, 255, 0.96);
}

.apexcharts-tooltip.apexcharts-theme-dark {
  color: #fff;
  background: rgba(30, 30, 30, 0.8);
}

.apexcharts-tooltip * {
  font-family: inherit;
}

.apexcharts-tooltip-title {
  padding: 6px;
  font-size: 15px;
  margin-bottom: 4px;
}

.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  background: #eceff1;
  border-bottom: 1px solid #ddd;
}

.apexcharts-tooltip.apexcharts-theme-dark .apexcharts-tooltip-title {
  background: rgba(0, 0, 0, 0.7);
  border-bottom: 1px solid #333;
}

.apexcharts-tooltip-text-goals-value, .apexcharts-tooltip-text-y-value, .apexcharts-tooltip-text-z-value {
  display: inline-block;
  margin-left: 5px;
  font-weight: 600;
}

.apexcharts-tooltip-text-goals-label:empty, .apexcharts-tooltip-text-goals-value:empty, .apexcharts-tooltip-text-y-label:empty, .apexcharts-tooltip-text-y-value:empty, .apexcharts-tooltip-text-z-value:empty, .apexcharts-tooltip-title:empty {
  display: none;
}

.apexcharts-tooltip-text-goals-label, .apexcharts-tooltip-text-goals-value {
  padding: 6px 0 5px;
}

.apexcharts-tooltip-goals-group, .apexcharts-tooltip-text-goals-label, .apexcharts-tooltip-text-goals-value {
  display: flex;
}

.apexcharts-tooltip-text-goals-label:not(:empty), .apexcharts-tooltip-text-goals-value:not(:empty) {
  margin-top: -6px;
}

.apexcharts-tooltip-marker {
  width: 12px;
  height: 12px;
  position: relative;
  top: 0;
  margin-right: 10px;
  border-radius: 50%;
}

.apexcharts-tooltip-series-group {
  padding: 0 10px;
  display: none;
  text-align: left;
  justify-content: left;
  align-items: center;
}

.apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-marker {
  opacity: 1;
}

.apexcharts-tooltip-series-group.apexcharts-active, .apexcharts-tooltip-series-group:last-child {
  padding-bottom: 4px;
}

.apexcharts-tooltip-series-group-hidden {
  opacity: 0;
  height: 0;
  line-height: 0;
  padding: 0 !important;
}

.apexcharts-tooltip-y-group {
  padding: 6px 0 5px;
}

.apexcharts-custom-tooltip, .apexcharts-tooltip-box {
  padding: 4px 8px;
}

.apexcharts-tooltip-boxPlot {
  display: flex;
  flex-direction: column-reverse;
}

.apexcharts-tooltip-box > div {
  margin: 4px 0;
}

.apexcharts-tooltip-box span.value {
  font-weight: 700;
}

.apexcharts-tooltip-rangebar {
  padding: 5px 8px;
}

.apexcharts-tooltip-rangebar .category {
  font-weight: 600;
  color: #777;
}

.apexcharts-tooltip-rangebar .series-name {
  font-weight: 700;
  display: block;
  margin-bottom: 5px;
}

.apexcharts-xaxistooltip, .apexcharts-yaxistooltip {
  opacity: 0;
  pointer-events: none;
  color: #373d3f;
  font-size: 13px;
  text-align: center;
  border-radius: 2px;
  position: absolute;
  z-index: 10;
  background: #eceff1;
  border: 1px solid #90a4ae;
}

.apexcharts-xaxistooltip {
  padding: 9px 10px;
  transition: 0.15s ease all;
}

.apexcharts-xaxistooltip.apexcharts-theme-dark {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #fff;
}

.apexcharts-xaxistooltip:after, .apexcharts-xaxistooltip:before {
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.apexcharts-xaxistooltip:after {
  border-color: transparent;
  border-width: 6px;
  margin-left: -6px;
}

.apexcharts-xaxistooltip:before {
  border-color: transparent;
  border-width: 7px;
  margin-left: -7px;
}

.apexcharts-xaxistooltip-bottom:after, .apexcharts-xaxistooltip-bottom:before {
  bottom: 100%;
}

.apexcharts-xaxistooltip-top:after, .apexcharts-xaxistooltip-top:before {
  top: 100%;
}

.apexcharts-xaxistooltip-bottom:after {
  border-bottom-color: #eceff1;
}

.apexcharts-xaxistooltip-bottom:before {
  border-bottom-color: #90a4ae;
}

.apexcharts-xaxistooltip-bottom.apexcharts-theme-dark:after, .apexcharts-xaxistooltip-bottom.apexcharts-theme-dark:before {
  border-bottom-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip-top:after {
  border-top-color: #eceff1;
}

.apexcharts-xaxistooltip-top:before {
  border-top-color: #90a4ae;
}

.apexcharts-xaxistooltip-top.apexcharts-theme-dark:after, .apexcharts-xaxistooltip-top.apexcharts-theme-dark:before {
  border-top-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-yaxistooltip {
  padding: 4px 10px;
}

.apexcharts-yaxistooltip.apexcharts-theme-dark {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #fff;
}

.apexcharts-yaxistooltip:after, .apexcharts-yaxistooltip:before {
  top: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.apexcharts-yaxistooltip:after {
  border-color: transparent;
  border-width: 6px;
  margin-top: -6px;
}

.apexcharts-yaxistooltip:before {
  border-color: transparent;
  border-width: 7px;
  margin-top: -7px;
}

.apexcharts-yaxistooltip-left:after, .apexcharts-yaxistooltip-left:before {
  left: 100%;
}

.apexcharts-yaxistooltip-right:after, .apexcharts-yaxistooltip-right:before {
  right: 100%;
}

.apexcharts-yaxistooltip-left:after {
  border-left-color: #eceff1;
}

.apexcharts-yaxistooltip-left:before {
  border-left-color: #90a4ae;
}

.apexcharts-yaxistooltip-left.apexcharts-theme-dark:after, .apexcharts-yaxistooltip-left.apexcharts-theme-dark:before {
  border-left-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip-right:after {
  border-right-color: #eceff1;
}

.apexcharts-yaxistooltip-right:before {
  border-right-color: #90a4ae;
}

.apexcharts-yaxistooltip-right.apexcharts-theme-dark:after, .apexcharts-yaxistooltip-right.apexcharts-theme-dark:before {
  border-right-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip.apexcharts-active {
  opacity: 1;
}

.apexcharts-yaxistooltip-hidden {
  display: none;
}

.apexcharts-xcrosshairs, .apexcharts-ycrosshairs {
  pointer-events: none;
  opacity: 0;
  transition: 0.15s ease all;
}

.apexcharts-xcrosshairs.apexcharts-active, .apexcharts-ycrosshairs.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-ycrosshairs-hidden {
  opacity: 0;
}

.apexcharts-selection-rect {
  cursor: move;
}

.svg_select_boundingRect, .svg_select_points_rot {
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
}

.apexcharts-selection-rect + g .svg_select_boundingRect, .apexcharts-selection-rect + g .svg_select_points_rot {
  opacity: 0;
  visibility: hidden;
}

.apexcharts-selection-rect + g .svg_select_points_l, .apexcharts-selection-rect + g .svg_select_points_r {
  cursor: ew-resize;
  opacity: 1;
  visibility: visible;
}

.svg_select_points {
  fill: #efefef;
  stroke: #333;
  rx: 2;
}

.apexcharts-svg.apexcharts-zoomable.hovering-zoom {
  cursor: crosshair;
}

.apexcharts-svg.apexcharts-zoomable.hovering-pan {
  cursor: move;
}

.apexcharts-menu-icon, .apexcharts-pan-icon, .apexcharts-reset-icon, .apexcharts-selection-icon, .apexcharts-toolbar-custom-icon, .apexcharts-zoom-icon, .apexcharts-zoomin-icon, .apexcharts-zoomout-icon {
  cursor: pointer;
  width: 20px;
  height: 20px;
  line-height: 24px;
  color: #6e8192;
  text-align: center;
}

.apexcharts-menu-icon svg, .apexcharts-reset-icon svg, .apexcharts-zoom-icon svg, .apexcharts-zoomin-icon svg, .apexcharts-zoomout-icon svg {
  fill: #6e8192;
}

.apexcharts-selection-icon svg {
  fill: #444;
  transform: scale(0.76);
}

.apexcharts-theme-dark .apexcharts-menu-icon svg, .apexcharts-theme-dark .apexcharts-pan-icon svg, .apexcharts-theme-dark .apexcharts-reset-icon svg, .apexcharts-theme-dark .apexcharts-selection-icon svg, .apexcharts-theme-dark .apexcharts-toolbar-custom-icon svg, .apexcharts-theme-dark .apexcharts-zoom-icon svg, .apexcharts-theme-dark .apexcharts-zoomin-icon svg, .apexcharts-theme-dark .apexcharts-zoomout-icon svg {
  fill: #f3f4f5;
}

.apexcharts-canvas .apexcharts-reset-zoom-icon.apexcharts-selected svg, .apexcharts-canvas .apexcharts-selection-icon.apexcharts-selected svg, .apexcharts-canvas .apexcharts-zoom-icon.apexcharts-selected svg {
  fill: #008ffb;
}

.apexcharts-theme-light .apexcharts-menu-icon:hover svg, .apexcharts-theme-light .apexcharts-reset-icon:hover svg, .apexcharts-theme-light .apexcharts-selection-icon:not(.apexcharts-selected):hover svg, .apexcharts-theme-light .apexcharts-zoom-icon:not(.apexcharts-selected):hover svg, .apexcharts-theme-light .apexcharts-zoomin-icon:hover svg, .apexcharts-theme-light .apexcharts-zoomout-icon:hover svg {
  fill: #333;
}

.apexcharts-menu-icon, .apexcharts-selection-icon {
  position: relative;
}

.apexcharts-reset-icon {
  margin-left: 5px;
}

.apexcharts-menu-icon, .apexcharts-reset-icon, .apexcharts-zoom-icon {
  transform: scale(0.85);
}

.apexcharts-zoomin-icon, .apexcharts-zoomout-icon {
  transform: scale(0.7);
}

.apexcharts-zoomout-icon {
  margin-right: 3px;
}

.apexcharts-pan-icon {
  transform: scale(0.62);
  position: relative;
  left: 1px;
  top: 0;
}

.apexcharts-pan-icon svg {
  fill: #fff;
  stroke: #6e8192;
  stroke-width: 2;
}

.apexcharts-pan-icon.apexcharts-selected svg {
  stroke: #008ffb;
}

.apexcharts-pan-icon:not(.apexcharts-selected):hover svg {
  stroke: #333;
}

.apexcharts-toolbar {
  position: absolute;
  z-index: 11;
  max-width: 176px;
  text-align: right;
  border-radius: 3px;
  padding: 0 6px 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.apexcharts-menu {
  background: #fff;
  position: absolute;
  top: 100%;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 3px;
  right: 10px;
  opacity: 0;
  min-width: 110px;
  transition: 0.15s ease all;
  pointer-events: none;
}

.apexcharts-menu.apexcharts-menu-open {
  opacity: 1;
  pointer-events: all;
  transition: 0.15s ease all;
}

.apexcharts-menu-item {
  padding: 6px 7px;
  font-size: 12px;
  cursor: pointer;
}

.apexcharts-theme-light .apexcharts-menu-item:hover {
  background: #eee;
}

.apexcharts-theme-dark .apexcharts-menu {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
}

@media screen and (min-width: 768px) {
  .apexcharts-canvas:hover .apexcharts-toolbar {
    opacity: 1;
  }
}
.apexcharts-canvas .apexcharts-element-hidden, .apexcharts-datalabel.apexcharts-element-hidden, .apexcharts-hide .apexcharts-series-points {
  opacity: 0;
}

.apexcharts-datalabel, .apexcharts-datalabel-label, .apexcharts-datalabel-value, .apexcharts-datalabels, .apexcharts-pie-label {
  cursor: default;
  pointer-events: none;
}

.apexcharts-pie-label-delay {
  opacity: 0;
  animation-name: opaque;
  animation-duration: 0.3s;
  animation-fill-mode: forwards;
  animation-timing-function: ease;
}

.apexcharts-annotation-rect, .apexcharts-area-series .apexcharts-area, .apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events, .apexcharts-gridline, .apexcharts-line, .apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events, .apexcharts-point-annotation-label, .apexcharts-radar-series path, .apexcharts-radar-series polygon, .apexcharts-toolbar svg, .apexcharts-tooltip .apexcharts-marker, .apexcharts-xaxis-annotation-label, .apexcharts-yaxis-annotation-label, .apexcharts-zoom-rect {
  pointer-events: none;
}

.apexcharts-marker {
  transition: 0.15s ease all;
}

.resize-triggers {
  animation: 1ms resizeanim;
  visibility: hidden;
  opacity: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.contract-trigger:before, .resize-triggers, .resize-triggers > div {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.resize-triggers > div {
  height: 100%;
  width: 100%;
  background: #eee;
  overflow: auto;
}

.contract-trigger:before {
  overflow: hidden;
  width: 200%;
  height: 200%;
}