var Homepage=function(){if(homeInit=function(){document.getElementById("topbar-header"),document.querySelector(".ecom-dropdownmenu-fixed");document.querySelector(".app-adminwrap-layout");let e=document.querySelector(".sale-page"),t=(document.querySelector(".homePageFour"),document.querySelector(".box-toggle")),s=document.getElementById("sale-page-top-one"),o=document.getElementById("sale-page-top-two"),i=document.querySelector(".slider-sec-1"),r=document.querySelector(".slider-sec-2"),n=document.querySelector(".slider-sec-3-1"),a=document.querySelector(".slider-sec-3-2"),l=document.querySelector(".slider-sec-3"),c=document.querySelector(".slider-sec-4"),m=document.querySelector(".slider-sec-5"),d=document.querySelector(".slider-sec-6"),u=document.querySelector(".slider-sec-7"),p=document.querySelector(".slider-sec-8"),y=document.querySelector(".slider-sec-9"),g=document.querySelector(".slider-sec-10"),v=document.querySelector(".slider-sec-11"),f=document.querySelector("#customize"),h=document.getElementById("tree"),S=document.querySelector(".perfect-scrollbar"),q=document.querySelectorAll(".box"),b=document.querySelector(".thumb-img");if(b){let e=b.children;q.forEach((t=>{t.addEventListener("click",(s=>{document.querySelector(".pro-img").src=t.children[0].src;for(let t=0;t<e.length;t++)e[t].classList.remove("active");t.classList.add("active")}))}))}var x=document.body;if(t&&t.addEventListener("click",(function(){var e=document.querySelector(".box-sidebar"),t=e.className.split(" "),s=t.indexOf("open");s>=0?t.splice(s,1):t.push("open"),e.className=t.join(" ")})),S){new PerfectScrollbar(".perfect-scrollbar")}if(window.onscroll=function(){s&&(window.pageYOffset>=300?x.classList.add("sticky-sale-page"):x.classList.remove("sticky-sale-page")),o&&(window.pageYOffset>=170?x.classList.add("sticky-sale-page-two"):x.classList.remove("sticky-sale-page-two")),e||(window.pageYOffset>=150?x.classList.add("sticky-header"):x.classList.remove("sticky-header"))},i)tns({container:".slider-sec-1",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!1,controlsText:[" ",""],responsive:{1400:{items:4},1024:{items:4},768:{items:3},600:{items:2},375:{items:2}}});if(r)tns({container:".slider-sec-2",loop:!1,gutter:30,slideBy:"page",autoplay:!1,nav:!1,arrowKeys:!0,controlsText:[" ",""],responsive:{1400:{items:3},949:{items:2}}});if(l)tns({container:".slider-sec-3",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!1,controlsText:[" ",""],responsive:{1400:{items:6},1024:{items:6},800:{items:4},799:{items:3},650:{items:3},649:{items:2},320:{items:2}}});if(n)tns({container:".slider-sec-3-1",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:1},1024:{items:1},768:{items:1}}});if(a)tns({container:".slider-sec-3-2",gutter:20,loop:!1,slideBy:"page",autoplay:!1,nav:!0,controls:!1,controlsText:[" ",""],responsive:{1400:{items:1},1024:{items:1},768:{items:1}}});if(c)tns({container:".slider-sec-4",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!1,controlsText:[" ",""],responsive:{1400:{items:4},1024:{items:3},768:{items:2}}});if(m)tns({container:".slider-sec-5",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!1,controlsText:[" ",""],responsive:{1400:{items:4},1024:{items:4},768:{items:3},600:{items:2},375:{items:2}}});if(d)tns({container:".slider-sec-6",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:3},1024:{items:3},992:{items:3},700:{items:3},699:{items:2},500:{items:2}}});if(u)tns({container:".slider-sec-7",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:3},1024:{items:3},992:{items:3},700:{items:3},699:{items:2},500:{items:2}}});if(p)tns({container:".slider-sec-8",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:1},1024:{items:1},768:{items:1}}});if(y)tns({container:".slider-sec-9",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:3},1024:{items:3},992:{items:3},700:{items:3},699:{items:2},500:{items:2}}});if(g)tns({container:".slider-sec-10",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:3},1024:{items:3},992:{items:3},700:{items:3},699:{items:2},500:{items:2}}});if(v)tns({container:".slider-sec-11",loop:!1,gutter:20,slideBy:"page",autoplay:!1,nav:!0,controlsText:[" ",""],responsive:{1400:{items:1},1024:{items:1},768:{items:1}}});if(f)tns({container:"#customize",controlsContainer:"#customize-controls",navContainer:"#customize-thumbnails",autoplayButton:"#customize-toggle",navAsThumbnails:!0,autoplay:!1,nav:!0,items:3,controlsText:[" ",""]});if(h)new TreeView([{name:"Fragrance",children:[]},{name:"Bath Preparations",expanded:!0,children:[{name:"Bubble Bath",children:[]},{name:"Bath Capsules",children:[]},{name:"Others",children:[{name:"Bubble Bath"}]}]},{name:"Eye Makeup Preparations",children:[]}],"tree");if(document.querySelector(".double-range")){new Rangeable(".double-range",{type:"double",tooltips:"always",min:0,max:100,value:[22,74]})}[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(e){return new bootstrap.Tooltip(e)}))},document.querySelector("#chart")){new ApexCharts(document.querySelector("#chart"),{colors:["#D23F57"],series:[{name:"series1",data:[70,60,80,51,42,109,100,50,80]}],chart:{height:265,type:"area",toolbar:{show:!1}},dataLabels:{enabled:!1},stroke:{curve:"smooth"},grid:{show:!1},xaxis:{type:"datetime",categories:["2018-09-19T00:00:00.000Z","2018-09-19T01:30:00.000Z","2018-09-19T02:30:00.000Z","2018-09-19T03:30:00.000Z","2018-09-19T04:30:00.000Z","2018-09-19T05:30:00.000Z","2018-09-19T06:30:00.000Z","2018-09-19T07:30:00.000Z","2018-09-19T08:30:00.000Z"],labels:{show:!1}},yaxis:{show:!1},markers:{size:4},stroke:{width:1},tooltip:{x:{format:"dd/MM/yy HH:mm"}}}).render()}let e=document.querySelector(".homepage-four-button-toggle");return e&&e.addEventListener("click",(function(){var e=document.querySelector(".homepage-four-sidebar"),t=e.className.split(" "),s=t.indexOf("open");s>=0?t.splice(s,1):t.push("open"),e.className=t.join(" ")})),{init:function(){homeInit()}}}();"loading"!==document.readyState?Homepage.init():document.addEventListener("DOMContentLoaded",Homepage.init);