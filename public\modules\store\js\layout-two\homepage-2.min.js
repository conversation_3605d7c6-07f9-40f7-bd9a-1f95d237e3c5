var Homepage=(homeInit=function(){let e=document.querySelector(".header-2");var t=document.body;let n=document.querySelector(".box-toggle");n&&n.addEventListener("click",(function(){var e=document.querySelector(".box-sidebar"),t=e.className.split(" "),n=t.indexOf("open");n>=0?t.splice(n,1):t.push("open"),e.className=t.join(" ")})),document.querySelector(".double-range-resturant")&&new Rangeable(".double-range-resturant",{type:"double",tooltips:"always",min:0,max:100,value:[22,74]}),window.onscroll=function(){window.pageYOffset>e.offsetHeight?t.classList.add("sticky-header-2"):t.classList.remove("sticky-header-2")}},{init:function(){homeInit()}});"loading"!==document.readyState?Homepage.init():document.addEventListener("DOMContentLoaded",Homepage.init);