<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Passport\Console\ClientCommand;
use <PERSON><PERSON>\Passport\Console\InstallCommand;
use <PERSON><PERSON>\Passport\Console\KeysCommand;
use <PERSON><PERSON>\Passport\Passport;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Request;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);
         /*ADD THIS LINES*/
        $this->commands([
            InstallCommand::class,
            ClientCommand::class,
            KeysCommand::class,
        ]);

       View::composer('*', function ($view) {
          $excluded = [
              'api',
              'setup',
              'update',
              'update_database_module',
              'password',
              'module',
              'store',
              'online_store',
          ];

          $firstSegment = Request::segment(1); // Get the first segment of the URL

          if (!in_array($firstSegment, $excluded)) {
              $view->with('app_settings', Setting::first());
          }
      });

         // Set the default guard to 'store' for all 'store/*' routes
        $this->app['router']->matched(function (\Illuminate\Routing\Events\RouteMatched $event) {
            if ($event->route->action['middleware'] === 'auth.store') {
                Auth::shouldUse('store');
            }
        });
    }
}
