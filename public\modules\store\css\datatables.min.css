/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#bs5/jszip-2.5.0/dt-1.13.1/af-2.5.1/b-2.3.3/b-colvis-2.3.3/b-html5-2.3.3/b-print-2.3.3/cr-1.6.1/fc-4.2.1/fh-3.3.1/kt-2.8.0/r-2.4.0/rg-1.3.0/rr-1.3.1/sc-2.0.7/sl-1.5.0
 *
 * Included libraries:
 *  JSZip 2.5.0, DataTables 1.13.1, AutoFill 2.5.1, Buttons 2.3.3, Column visibility 2.3.3, HTML5 export 2.3.3, Print view 2.3.3, ColReorder 1.6.1, FixedColumns 4.2.1, FixedHeader 3.3.1, KeyTable 2.8.0, Responsive 2.4.0, RowGroup 1.3.0, <PERSON><PERSON><PERSON><PERSON> 1.3.1, <PERSON><PERSON><PERSON> 2.0.7, Select 1.5.0
 */


 .dataTable {
    width: 100% !important;
}

table.dataTable td.dt-control {
    text-align: center;
    cursor: pointer;
}

table.dataTable thead {
    font-size: 13px;
}

table.dataTable thead th {
    width: 99px !important;
    color: #496ab59e;
    text-transform: capitalize;
    white-space: nowrap;
    padding: 12px 6px;
    border: 0 solid transparent;
    border-bottom: 1px solid #ddd !important;
}

table.dataTable td.dt-control {
    text-align: center;
    cursor: pointer;
}
table.dataTable td.dt-control:before {
    height: 1em;
    width: 1em;
    margin-top: -9px;
    display: inline-block;
    color: white;
    border: 0.15em solid white;
    border-radius: 1em;
    box-shadow: 0 0 0.2em #444;
    box-sizing: content-box;
    text-align: center;
    text-indent: 0 !important;
    font-family: "Courier New", Courier, monospace;
    line-height: 1em;
    content: "+";
    background-color: #31b131;
}
table.dataTable tr.dt-hasChild td.dt-control:before {
    content: "-";
    background-color: #d33333;
}
table.dataTable thead > tr > th.sorting,
table.dataTable thead > tr > th.sorting_asc,
table.dataTable thead > tr > th.sorting_desc,
table.dataTable thead > tr > th.sorting_asc_disabled,
table.dataTable thead > tr > th.sorting_desc_disabled,
table.dataTable thead > tr > td.sorting,
table.dataTable thead > tr > td.sorting_asc,
table.dataTable thead > tr > td.sorting_desc,
table.dataTable thead > tr > td.sorting_asc_disabled,
table.dataTable thead > tr > td.sorting_desc_disabled {
    cursor: pointer;
    position: relative;
    padding-right: 26px;
}
table.dataTable thead > tr > th.sorting:before,
table.dataTable thead > tr > th.sorting:after,
table.dataTable thead > tr > th.sorting_asc:before,
table.dataTable thead > tr > th.sorting_asc:after,
table.dataTable thead > tr > th.sorting_desc:before,
table.dataTable thead > tr > th.sorting_desc:after,
table.dataTable thead > tr > th.sorting_asc_disabled:before,
table.dataTable thead > tr > th.sorting_asc_disabled:after,
table.dataTable thead > tr > th.sorting_desc_disabled:before,
table.dataTable thead > tr > th.sorting_desc_disabled:after,
table.dataTable thead > tr > td.sorting:before,
table.dataTable thead > tr > td.sorting:after,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_asc:after,
table.dataTable thead > tr > td.sorting_desc:before,
table.dataTable thead > tr > td.sorting_desc:after,
table.dataTable thead > tr > td.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_asc_disabled:after,
table.dataTable thead > tr > td.sorting_desc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:after {
    position: absolute;
    display: block;
    opacity: 0.125;
    right: 10px;
    line-height: 9px;
    font-size: 0.8em;
}
table.dataTable thead > tr > th.sorting:before,
table.dataTable thead > tr > th.sorting_asc:before,
table.dataTable thead > tr > th.sorting_desc:before,
table.dataTable thead > tr > th.sorting_asc_disabled:before,
table.dataTable thead > tr > th.sorting_desc_disabled:before,
table.dataTable thead > tr > td.sorting:before,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_desc:before,
table.dataTable thead > tr > td.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:before {
    bottom: 50%;
    content: "▲";
}
table.dataTable thead > tr > th.sorting:after,
table.dataTable thead > tr > th.sorting_asc:after,
table.dataTable thead > tr > th.sorting_desc:after,
table.dataTable thead > tr > th.sorting_asc_disabled:after,
table.dataTable thead > tr > th.sorting_desc_disabled:after,
table.dataTable thead > tr > td.sorting:after,
table.dataTable thead > tr > td.sorting_asc:after,
table.dataTable thead > tr > td.sorting_desc:after,
table.dataTable thead > tr > td.sorting_asc_disabled:after,
table.dataTable thead > tr > td.sorting_desc_disabled:after {
    top: 50%;
    content: "▼";
}
table.dataTable thead > tr > th.sorting_asc:before,
table.dataTable thead > tr > th.sorting_desc:after,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_desc:after {
    opacity: 0.6;
}
table.dataTable thead > tr > th.sorting_desc_disabled:after,
table.dataTable thead > tr > th.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:after,
table.dataTable thead > tr > td.sorting_asc_disabled:before {
    display: none;
}
table.dataTable thead > tr > th:active,
table.dataTable thead > tr > td:active {
    outline: none;
}
div.dataTables_scrollBody table.dataTable thead > tr > th:before,
div.dataTables_scrollBody table.dataTable thead > tr > th:after,
div.dataTables_scrollBody table.dataTable thead > tr > td:before,
div.dataTables_scrollBody table.dataTable thead > tr > td:after {
    display: none;
}
div.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 2px;
}
div.dataTables_processing > div:last-child {
    position: relative;
    width: 80px;
    height: 15px;
    margin: 1em auto;
}
div.dataTables_processing > div:last-child > div {
    position: absolute;
    top: 0;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: rgba(13, 110, 253, 0.9);
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}
div.dataTables_processing > div:last-child > div:nth-child(1) {
    left: 8px;
    animation: datatables-loader-1 0.6s infinite;
}
div.dataTables_processing > div:last-child > div:nth-child(2) {
    left: 8px;
    animation: datatables-loader-2 0.6s infinite;
}
div.dataTables_processing > div:last-child > div:nth-child(3) {
    left: 32px;
    animation: datatables-loader-2 0.6s infinite;
}
div.dataTables_processing > div:last-child > div:nth-child(4) {
    left: 56px;
    animation: datatables-loader-3 0.6s infinite;
}
@keyframes datatables-loader-1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}
@keyframes datatables-loader-3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}
@keyframes datatables-loader-2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}
table.dataTable.nowrap th,
table.dataTable.nowrap td {
    white-space: nowrap;
}
table.dataTable th.dt-left,
table.dataTable td.dt-left {
    text-align: left;
}
table.dataTable th.dt-center,
table.dataTable td.dt-center,
table.dataTable td.dataTables_empty {
    text-align: center;
}
table.dataTable th.dt-right,
table.dataTable td.dt-right {
    text-align: right;
}
table.dataTable th.dt-justify,
table.dataTable td.dt-justify {
    text-align: justify;
}
table.dataTable th.dt-nowrap,
table.dataTable td.dt-nowrap {
    white-space: nowrap;
}
table.dataTable thead th,
table.dataTable thead td,
table.dataTable tfoot th,
table.dataTable tfoot td {
    text-align: left;
}
table.dataTable thead th.dt-head-left,
table.dataTable thead td.dt-head-left,
table.dataTable tfoot th.dt-head-left,
table.dataTable tfoot td.dt-head-left {
    text-align: left;
}
table.dataTable thead th.dt-head-center,
table.dataTable thead td.dt-head-center,
table.dataTable tfoot th.dt-head-center,
table.dataTable tfoot td.dt-head-center {
    text-align: center;
}
table.dataTable thead th.dt-head-right,
table.dataTable thead td.dt-head-right,
table.dataTable tfoot th.dt-head-right,
table.dataTable tfoot td.dt-head-right {
    text-align: right;
}
table.dataTable thead th.dt-head-justify,
table.dataTable thead td.dt-head-justify,
table.dataTable tfoot th.dt-head-justify,
table.dataTable tfoot td.dt-head-justify {
    text-align: justify;
}
table.dataTable thead th.dt-head-nowrap,
table.dataTable thead td.dt-head-nowrap,
table.dataTable tfoot th.dt-head-nowrap,
table.dataTable tfoot td.dt-head-nowrap {
    white-space: nowrap;
}
table.dataTable tbody th.dt-body-left,
table.dataTable tbody td.dt-body-left {
    text-align: left;
}
table.dataTable tbody th.dt-body-center,
table.dataTable tbody td.dt-body-center {
    text-align: center;
}
table.dataTable tbody th.dt-body-right,
table.dataTable tbody td.dt-body-right {
    text-align: right;
}
table.dataTable tbody th.dt-body-justify,
table.dataTable tbody td.dt-body-justify {
    text-align: justify;
}
table.dataTable tbody th.dt-body-nowrap,
table.dataTable tbody td.dt-body-nowrap {
    white-space: nowrap;
} /*! Bootstrap 5 integration for DataTables
 *
 * ©2020 SpryMedia Ltd, all rights reserved.
 * License: MIT datatables.net/license/mit
 */
table.dataTable {
    clear: both;
    margin-top: 6px !important;
    margin-bottom: 6px !important;
    max-width: none !important;
    border-collapse: separate !important;
    border-spacing: 0;
}
table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}
table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
    text-align: center;
}
table.dataTable.nowrap th,
table.dataTable.nowrap td {
    white-space: nowrap;
}
table.dataTable.table-striped > tbody > tr:nth-of-type(2n + 1) > * {
    box-shadow: none;
}
table.dataTable > tbody > tr {
    background-color: transparent;
}
table.dataTable > tbody > tr.selected > * {
    box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.9);
    color: white;
}
table.dataTable > tbody > tr.selected a {
    color: #090a0b;
}
table.dataTable.table-striped > tbody > tr.odd > * {
    box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.05);
}
table.dataTable.table-striped > tbody > tr.odd.selected > * {
    box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.95);
}
table.dataTable.table-hover > tbody > tr:hover > * {
    box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.075);
}
table.dataTable.table-hover > tbody > tr.selected:hover > * {
    box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.975);
}

div.dataTables_wrapper div.dataTables_length {
    float: left;
    margin-right: 5px;
}

div.dataTables_wrapper div.dataTables_length label {
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
}
div.dataTables_wrapper div.dataTables_length select {
    width: 75px;
    display: inline-block;
    height: 33px!important;
    border-color: #8590a7!important;
    color: #8590a7!important;
    font-size: 13px;
    padding: 6px 15px!important;
    border-radius: 6px;
}
div.dataTables_wrapper div.dataTables_filter {
    text-align: right;
}
div.dataTables_wrapper div.dataTables_filter label {
    font-weight: normal;
    white-space: nowrap;
    text-align: left;
}
div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0.5em;
    display: inline-block;
    width: auto;
}
div.dataTables_wrapper div.dataTables_info {
    padding-top: 0.85em;
}
div.dataTables_wrapper div.dataTables_paginate {
    margin: 0;
    white-space: nowrap;
    text-align: right;
}
div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
    justify-content: flex-end;
}
div.dataTables_wrapper div.dt-row {
    position: relative;
}
div.dataTables_scrollHead table.dataTable {
    margin-bottom: 0 !important;
}
div.dataTables_scrollBody > table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}
div.dataTables_scrollBody > table > thead .sorting:before,
div.dataTables_scrollBody > table > thead .sorting_asc:before,
div.dataTables_scrollBody > table > thead .sorting_desc:before,
div.dataTables_scrollBody > table > thead .sorting:after,
div.dataTables_scrollBody > table > thead .sorting_asc:after,
div.dataTables_scrollBody > table > thead .sorting_desc:after {
    display: none;
}
div.dataTables_scrollBody > table > tbody tr:first-child th,
div.dataTables_scrollBody > table > tbody tr:first-child td {
    border-top: none;
}
div.dataTables_scrollFoot > .dataTables_scrollFootInner {
    box-sizing: content-box;
}
div.dataTables_scrollFoot > .dataTables_scrollFootInner > table {
    margin-top: 0 !important;
    border-top: none;
}
@media screen and (max-width: 767px) {
    div.dataTables_wrapper div.dataTables_length,
    div.dataTables_wrapper div.dataTables_filter,
    div.dataTables_wrapper div.dataTables_info,
    div.dataTables_wrapper div.dataTables_paginate {
        text-align: center;
    }
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        justify-content: center !important;
    }
}
table.dataTable.table-sm > thead > tr > th:not(.sorting_disabled) {
    padding-right: 20px;
}
table.table-bordered.dataTable {
    border-right-width: 0;
}
table.table-bordered.dataTable thead tr:first-child th,
table.table-bordered.dataTable thead tr:first-child td {
    border-top-width: 1px;
}
table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
    border-left-width: 0;
}
table.table-bordered.dataTable th:first-child,
table.table-bordered.dataTable th:first-child,
table.table-bordered.dataTable td:first-child,
table.table-bordered.dataTable td:first-child {
    border-left-width: 1px;
}
table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
    border-right-width: 1px;
}
table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
    border-bottom-width: 1px;
}
div.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0;
}
div.table-responsive > div.dataTables_wrapper > div.row {
    margin: 0;
}
div.table-responsive
    > div.dataTables_wrapper
    > div.row
    > div[class^="col-"]:first-child {
    padding-left: 0;
}
div.table-responsive
    > div.dataTables_wrapper
    > div.row
    > div[class^="col-"]:last-child {
    padding-right: 0;
}

div.dt-autofill-handle {
    position: absolute;
    height: 8px;
    width: 8px;
    z-index: 102;
    box-sizing: border-box;
    background: #0d6efd;
    cursor: pointer;
}
div.dtk-focus-alt div.dt-autofill-handle {
    background: #ff8b33;
}
div.dt-autofill-select {
    position: absolute;
    z-index: 1001;
    background-color: #0d6efd;
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 5px,
        rgba(255, 255, 255, 0.5) 5px,
        rgba(255, 255, 255, 0.5) 10px
    );
}
div.dt-autofill-select.top,
div.dt-autofill-select.bottom {
    height: 3px;
    margin-top: -1px;
}
div.dt-autofill-select.left,
div.dt-autofill-select.right {
    width: 3px;
    margin-left: -1px;
}
div.dt-autofill-list {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 500px;
    margin-left: -250px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
    border: 1px solid black;
    z-index: 11;
    box-sizing: border-box;
    padding: 1.5em 2em;
    padding-top: 2em;
}
div.dt-autofill-list div.dtaf-popover-close {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 22px;
    height: 22px;
    border: 1px solid #eaeaea;
    background-color: #f9f9f9;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    z-index: 12;
}
div.dt-autofill-list ul {
    display: table;
    margin: 0;
    padding: 0;
    list-style: none;
    width: 100%;
}
div.dt-autofill-list ul li {
    display: table-row;
    cursor: pointer;
}
div.dt-autofill-list ul li:last-child div.dt-autofill-question,
div.dt-autofill-list ul li:last-child div.dt-autofill-button {
    border-bottom: none;
}
div.dt-autofill-list ul li:hover {
    background-color: #f6f6f6;
}
div.dt-autofill-list ul li:hover button.btn {
    background-color: #548bbb;
}
div.dt-autofill-list div.dt-autofill-question {
    display: table-cell;
    padding: 0.5em 0;
    padding-left: 5px;
    border-bottom: 1px solid #ccc;
}
div.dt-autofill-list div.dt-autofill-question input[type="number"] {
    padding: 6px;
    width: 30px;
    margin: -2px 0;
}
div.dt-autofill-list div.dt-autofill-button {
    display: table-cell;
    padding: 0.5em 0;
    padding-right: 5px;
    border-bottom: 1px solid #ccc;
    text-align: right;
}
div.dtaf-popover-closeable {
    padding-top: 2.5em;
}
div.dt-autofill-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    background: radial-gradient(
        ellipse farthest-corner at center,
        rgba(0, 0, 0, 0.3) 0%,
        rgba(0, 0, 0, 0.7) 100%
    );
    z-index: 10;
}
@media screen and (max-width: 767px) {
    div.dt-autofill-handle {
        height: 16px;
        width: 16px;
    }
    div.dt-autofill-list {
        width: 90%;
        left: 74.5%;
    }
}
div.dt-autofill-list div.dt-autofill-question input[type="number"] {
    padding: 6px;
    width: 60px;
    margin: -2px 0;
}
div.row.dt-row > div.col-sm-12 {
    position: relative;
}

@keyframes dtb-spinner {
    100% {
        transform: rotate(360deg);
    }
}
@-o-keyframes dtb-spinner {
    100% {
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-ms-keyframes dtb-spinner {
    100% {
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-webkit-keyframes dtb-spinner {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-moz-keyframes dtb-spinner {
    100% {
        -moz-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
div.dataTables_wrapper {
    position: relative;
}
div.dt-buttons {
    position: initial;
}
div.dt-buttons .dt-button {
    overflow: hidden;
    text-overflow: ellipsis;
}

div.dt-buttons>.btn-group>.btn.btn-secondary{
    border-color: #8590a7!important;
}
div.dt-button-info {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 400px;
    margin-top: -100px;
    margin-left: -200px;
    background-color: white;
    border: 2px solid #111;
    box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    text-align: center;
    z-index: 21;
}
div.dt-button-info h2 {
    padding: 0.5em;
    margin: 0;
    font-weight: normal;
    border-bottom: 1px solid #ddd;
    background-color: #f3f3f3;
}
div.dt-button-info > div {
    padding: 1em;
}
div.dtb-popover-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 22px;
    height: 22px;
    border: 1px solid #eaeaea;
    background-color: #f9f9f9;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    z-index: 2003;
}
button.dtb-hide-drop {
    display: none !important;
}
div.dt-button-collection-title {
    text-align: center;
    padding: 0.3em 0 0.5em;
    margin-left: 0.5em;
    margin-right: 0.5em;
    font-size: 0.9em;
}
div.dt-button-collection-title:empty {
    display: none;
}
span.dt-button-spacer {
    display: inline-block;
    margin: 0.5em;
    white-space: nowrap;
}
span.dt-button-spacer.bar {
    border-left: 1px solid rgba(0, 0, 0, 0.3);
    vertical-align: middle;
    padding-left: 0.5em;
}
span.dt-button-spacer.bar:empty {
    height: 1em;
    width: 1px;
    padding-left: 0;
}
div.dt-button-collection span.dt-button-spacer {
    width: 100%;
    font-size: 0.9em;
    text-align: center;
    margin: 0.5em 0;
}
div.dt-button-collection span.dt-button-spacer:empty {
    height: 0;
    width: 100%;
}
div.dt-button-collection span.dt-button-spacer.bar {
    border-left: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.3);
    padding-left: 0;
}
div.dt-button-collection {
    position: absolute;
    z-index: 2001;
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    padding: 0.5rem;
    width: 218px;
}
div.dt-button-collection div.dropdown-menu {
    position: relative;
    display: block;
    background-color: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
    border-radius: 0;
    z-index: 2002;
    min-width: 100%;
}
div.dt-button-collection.fixed {
    position: fixed;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: -75px;
    border-radius: 5px;
    background-color: white;
}
div.dt-button-collection.fixed.two-column {
    margin-left: -200px;
}
div.dt-button-collection.fixed.three-column {
    margin-left: -225px;
}
div.dt-button-collection.fixed.four-column {
    margin-left: -300px;
}
div.dt-button-collection.fixed.columns {
    margin-left: -409px;
}
@media screen and (max-width: 1024px) {
    div.dt-button-collection.fixed.columns {
        margin-left: -308px;
    }
}
@media screen and (max-width: 640px) {
    div.dt-button-collection.fixed.columns {
        margin-left: -203px;
    }
}
@media screen and (max-width: 460px) {
    div.dt-button-collection.fixed.columns {
        margin-left: -100px;
    }
}
div.dt-button-collection.fixed > :last-child {
    max-height: 100vh;
    overflow: auto;
}
div.dt-button-collection.two-column > :last-child,
div.dt-button-collection.three-column > :last-child,
div.dt-button-collection.four-column > :last-child {
    display: block !important;
    -webkit-column-gap: 8px;
    -moz-column-gap: 8px;
    -ms-column-gap: 8px;
    -o-column-gap: 8px;
    column-gap: 8px;
}
div.dt-button-collection.two-column > :last-child > *,
div.dt-button-collection.three-column > :last-child > *,
div.dt-button-collection.four-column > :last-child > * {
    -webkit-column-break-inside: avoid;
    break-inside: avoid;
}
div.dt-button-collection.two-column {
    width: 400px;
}
div.dt-button-collection.two-column > :last-child {
    padding-bottom: 1px;
    column-count: 2;
}
div.dt-button-collection.three-column {
    width: 450px;
}
div.dt-button-collection.three-column > :last-child {
    padding-bottom: 1px;
    column-count: 3;
}
div.dt-button-collection.four-column {
    width: 600px;
}
div.dt-button-collection.four-column > :last-child {
    padding-bottom: 1px;
    column-count: 4;
}
div.dt-button-collection .dt-button {
    border-radius: 0;
}
div.dt-button-collection.columns {
    width: auto;
}
div.dt-button-collection.columns > :last-child {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    gap: 6px;
    width: 818px;
    padding-bottom: 1px;
}
div.dt-button-collection.columns > :last-child .dt-button {
    min-width: 200px;
    flex: 0 1;
    margin: 0;
}
div.dt-button-collection.columns.dtb-b3 > :last-child,
div.dt-button-collection.columns.dtb-b2 > :last-child,
div.dt-button-collection.columns.dtb-b1 > :last-child {
    justify-content: space-between;
}
div.dt-button-collection.columns.dtb-b3 .dt-button {
    flex: 1 1 32%;
}
div.dt-button-collection.columns.dtb-b2 .dt-button {
    flex: 1 1 48%;
}
div.dt-button-collection.columns.dtb-b1 .dt-button {
    flex: 1 1 100%;
}
@media screen and (max-width: 1024px) {
    div.dt-button-collection.columns > :last-child {
        width: 612px;
    }
}
@media screen and (max-width: 640px) {
    div.dt-button-collection.columns > :last-child {
        width: 406px;
    }
    div.dt-button-collection.columns.dtb-b3 .dt-button {
        flex: 0 1 32%;
    }
}
@media screen and (max-width: 460px) {
    div.dt-button-collection.columns > :last-child {
        width: 200px;
    }
}
div.dt-button-collection.fixed:before,
div.dt-button-collection.fixed:after {
    display: none;
}
div.dt-button-collection .btn-group {
    flex: 1 1 auto;
}
div.dt-button-collection .dt-button:not(.dt-btn-split-drop) {
    min-width: 200px;
}
div.dt-button-collection div.dt-btn-split-wrapper {
    width: 100%;
}
div.dt-button-collection button.dt-btn-split-drop-button {
    width: 100%;
    color: #212529;
    border: none;
    background-color: white;
    border-radius: 0px;
    margin-left: 0px !important;
}
div.dt-button-collection button.dt-btn-split-drop-button:focus {
    border: none;
    border-radius: 0px;
    outline: none;
}
div.dt-button-collection button.dt-btn-split-drop-button:hover {
    background-color: #e9ecef;
}
div.dt-button-collection button.dt-btn-split-drop-button:active {
    background-color: #007bff !important;
}
div.dt-button-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
}
@media screen and (max-width: 767px) {
    div.dt-buttons {
        float: none;
        width: 100%;
        text-align: center;
        margin-bottom: 0.5em;
    }
    div.dt-buttons a.btn {
        float: none;
    }
}
div.dt-buttons button.btn.processing,
div.dt-buttons div.btn.processing,
div.dt-buttons a.btn.processing {
    color: rgba(0, 0, 0, 0.2);
}
div.dt-buttons button.btn.processing:after,
div.dt-buttons div.btn.processing:after,
div.dt-buttons a.btn.processing:after {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    box-sizing: border-box;
    display: block;
    content: " ";
    border: 2px solid #282828;
    border-radius: 50%;
    border-left-color: transparent;
    border-right-color: transparent;
    animation: dtb-spinner 1500ms infinite linear;
    -o-animation: dtb-spinner 1500ms infinite linear;
    -ms-animation: dtb-spinner 1500ms infinite linear;
    -webkit-animation: dtb-spinner 1500ms infinite linear;
    -moz-animation: dtb-spinner 1500ms infinite linear;
}
div.dt-buttons div.btn-group {
    position: initial;
}
div.dt-btn-split-wrapper button.dt-btn-split-drop {
    border-top-right-radius: 0.25rem !important;
    border-bottom-right-radius: 0.25rem !important;
}
div.dt-btn-split-wrapper:active:not(.disabled) button,
div.dt-btn-split-wrapper.active:not(.disabled) button {
    background-color: #5a6268;
    border-color: #545b62;
}
div.dt-btn-split-wrapper:active:not(.disabled) button.dt-btn-split-drop,
div.dt-btn-split-wrapper.active:not(.disabled) button.dt-btn-split-drop {
    box-shadow: none;
    background-color: #6c757d;
    border-color: #6c757d;
}
div.dt-btn-split-wrapper:active:not(.disabled) button:hover,
div.dt-btn-split-wrapper.active:not(.disabled) button:hover {
    background-color: #5a6268;
    border-color: #545b62;
}
div.dataTables_wrapper div.dt-buttons.btn-group div.btn-group {
    border-radius: 4px !important;
}
div.dataTables_wrapper div.dt-buttons.btn-group div.btn-group:last-child {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}
div.dataTables_wrapper div.dt-buttons.btn-group div.btn-group:first-child {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}
div.dataTables_wrapper
    div.dt-buttons.btn-group
    div.btn-group:last-child:first-child {
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
    border-top-right-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
}
div.dataTables_wrapper
    div.dt-buttons.btn-group
    div.btn-group
    button.dt-btn-split-drop:last-child {
    border: 1px solid #6c757d;
}
div.dataTables_wrapper
    div.dt-buttons.btn-group
    div.btn-group
    div.dt-btn-split-wrapper {
    border: none;
}
div.dt-button-collection div.btn-group {
    border-radius: 4px !important;
}
div.dt-button-collection div.btn-group button {
    border-radius: 4px;
}
div.dt-button-collection div.btn-group button:last-child {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}
div.dt-button-collection div.btn-group button:first-child {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}
div.dt-button-collection div.btn-group button:last-child:first-child {
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
    border-top-right-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
}
div.dt-button-collection div.btn-group button.dt-btn-split-drop:last-child {
    border: 1px solid #6c757d;
}
div.dt-button-collection div.btn-group div.dt-btn-split-wrapper {
    border: none;
}
span.dt-button-spacer.bar:empty {
    height: inherit;
}
div.dt-button-collection span.dt-button-spacer {
    padding-left: 1rem !important;
    text-align: left;
}

table.DTCR_clonedTable.dataTable {
    position: absolute !important;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 202;
}
div.DTCR_pointer {
    width: 1px;
    background-color: #0d6efd;
    z-index: 201;
}

table.dataTable thead tr > .dtfc-fixed-left,
table.dataTable thead tr > .dtfc-fixed-right,
table.dataTable tfoot tr > .dtfc-fixed-left,
table.dataTable tfoot tr > .dtfc-fixed-right {
    top: 0;
    bottom: 0;
    z-index: 3;
    background-color: white;
}
table.dataTable tbody tr > .dtfc-fixed-left,
table.dataTable tbody tr > .dtfc-fixed-right {
    z-index: 1;
    background-color: white;
}
div.dtfc-left-top-blocker,
div.dtfc-right-top-blocker {
    background-color: white;
}
div.dtfc-right-top-blocker,
div.dtfc-left-top-blocker {
    margin-top: 6px;
    border-bottom: 0px solid #ddd !important;
}
table.dataTable.table-bordered.dtfc-has-left {
    border-left: none;
}
div.dataTables_scroll.dtfc-has-left table.table-bordered {
    border-left: none;
}
div.dataTables_scrollBody {
    border-left: 1px solid #ddd !important;
}
div.dataTables_scrollFootInner table.table-bordered tr th:first-child,
div.dataTables_scrollHeadInner table.table-bordered tr th:first-child {
    border-left: 1px solid #ddd !important;
}

table.dataTable.fixedHeader-floating,
table.dataTable.fixedHeader-locked {
    background-color: white;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}
table.dataTable.fixedHeader-locked {
    position: absolute !important;
}
@media print {
    table.fixedHeader-floating {
        display: none;
    }
}

table.dataTable tbody th.focus,
table.dataTable tbody td.focus {
    outline: 2px solid #0d6efd;
    outline-offset: -2px;
}
div.dtk-focus-alt table.dataTable tbody th.focus,
div.dtk-focus-alt table.dataTable tbody td.focus {
    outline: 2px solid #ff8b33;
    outline-offset: -2px;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
    cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
    display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
    position: relative;
    padding-left: 30px;
    cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
    top: 50%;
    left: 5px;
    height: 1em;
    width: 1em;
    margin-top: -9px;
    display: block;
    position: absolute;
    color: white;
    border: 0.15em solid white;
    border-radius: 1em;
    box-shadow: 0 0 0.2em #444;
    box-sizing: content-box;
    text-align: center;
    text-indent: 0 !important;
    font-family: "Courier New", Courier, monospace;
    line-height: 1em;
    content: "+";
    background-color: #0d6efd;
}
table.dataTable.dtr-inline.collapsed
    > tbody
    > tr.parent
    > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed
    > tbody
    > tr.parent
    > th.dtr-control:before {
    content: "-";
    background-color: #d33333;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control {
    padding-left: 27px;
}
table.dataTable.dtr-inline.collapsed.compact
    > tbody
    > tr
    > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed.compact
    > tbody
    > tr
    > th.dtr-control:before {
    left: 4px;
    height: 14px;
    width: 14px;
    border-radius: 14px;
    line-height: 14px;
    text-indent: 3px;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control,
table.dataTable.dtr-column > tbody > tr > th.dtr-control,
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
    position: relative;
    cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
    top: 50%;
    left: 50%;
    height: 0.8em;
    width: 0.8em;
    margin-top: -0.5em;
    margin-left: -0.5em;
    display: block;
    position: absolute;
    color: white;
    border: 0.15em solid white;
    border-radius: 1em;
    box-shadow: 0 0 0.2em #444;
    box-sizing: content-box;
    text-align: center;
    text-indent: 0 !important;
    font-family: "Courier New", Courier, monospace;
    line-height: 1em;
    content: "+";
    background-color: #0d6efd;
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
    content: "-";
    background-color: #d33333;
}
table.dataTable > tbody > tr.child {
    padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
    background: transparent !important;
}
table.dataTable > tbody > tr.child ul.dtr-details {
    display: inline-block;
    list-style-type: none;
    margin: 0;
    padding: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li {
    border-bottom: 1px solid #efefef;
    padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
    padding-top: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
    border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
    display: inline-block;
    min-width: 75px;
    font-weight: bold;
}
div.dtr-modal {
    position: fixed;
    box-sizing: border-box;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 100;
    padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 50%;
    height: 50%;
    overflow: auto;
    margin: auto;
    z-index: 102;
    overflow: auto;
    background-color: #f5f5f7;
    border: 1px solid black;
    border-radius: 0.5em;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
    position: relative;
    padding: 1em;
}
div.dtr-modal div.dtr-modal-close {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 22px;
    height: 22px;
    border: 1px solid #eaeaea;
    background-color: #f9f9f9;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    z-index: 12;
}
div.dtr-modal div.dtr-modal-close:hover {
    background-color: #eaeaea;
}
div.dtr-modal div.dtr-modal-background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 101;
    background: rgba(0, 0, 0, 0.6);
}
@media screen and (max-width: 767px) {
    div.dtr-modal div.dtr-modal-display {
        width: 95%;
    }
}
div.dtr-bs-modal table.table tr:first-child td {
    border-top: none;
}
table.dataTable.table-bordered th.dtr-control.dtr-hidden + *,
table.dataTable.table-bordered td.dtr-control.dtr-hidden + * {
    border-left-width: 1px;
}

table.dataTable tr.dtrg-group th {
    background-color: #e0e0e0;
    text-align: left;
}
table.dataTable tr.dtrg-group.dtrg-level-0 th {
    font-weight: bold;
}
table.dataTable tr.dtrg-group.dtrg-level-1 th,
table.dataTable tr.dtrg-group.dtrg-level-2 th,
table.dataTable tr.dtrg-group.dtrg-level-3 th,
table.dataTable tr.dtrg-group.dtrg-level-4 th,
table.dataTable tr.dtrg-group.dtrg-level-5 th {
    background-color: #f0f0f0;
    padding-top: 0.25em;
    padding-bottom: 0.25em;
    padding-left: 2em;
    font-size: 0.9em;
}
table.dataTable tr.dtrg-group.dtrg-level-2 th {
    background-color: #f3f3f3;
    padding-left: 2.5em;
}
table.dataTable tr.dtrg-group.dtrg-level-3 th {
    background-color: #f3f3f3;
    padding-left: 3em;
}
table.dataTable tr.dtrg-group.dtrg-level-4 th {
    background-color: #f3f3f3;
    padding-left: 3.5em;
}
table.dataTable tr.dtrg-group.dtrg-level-5 th {
    background-color: #f3f3f3;
    padding-left: 4em;
}
table.dataTable.table-striped tr.dtrg-level-0 {
    background-color: #e0e0e0;
}
table.dataTable.table-striped tr.dtrg-level-1 {
    background-color: #f0f0f0;
}
table.dataTable.table-striped tr.dtrg-level-2,
table.dataTable.table-striped tr.dtrg-level-3,
table.dataTable.table-striped tr.dtrg-level-4,
table.dataTable.table-striped tr.dtrg-level-5 {
    background-color: #f3f3f3;
}
table.dataTable.table-striped tr.dtrg-level-1 tr.dtrg-level-2 th,
table.dataTable.table-striped tr.dtrg-level-3 th,
table.dataTable.table-striped tr.dtrg-level-4 th,
table.dataTable.table-striped tr.dtrg-level-5 th {
    background-color: transparent;
}

table.dt-rowReorder-float {
    opacity: 0.8;
}
div.dt-rowReorder-float-parent {
    table-layout: fixed;
    outline: 2px solid #0d6efd;
    outline-offset: -2px;
    z-index: 2001;
    position: absolute !important;
    overflow: hidden;
}
tr.dt-rowReorder-moving {
    outline: 2px solid #888;
    outline-offset: -2px;
}
body.dt-rowReorder-noOverflow {
    overflow-x: hidden;
}
table.dataTable td.reorder {
    text-align: center;
    cursor: move;
}

div.dts {
    display: block !important;
}
div.dts tbody th,
div.dts tbody td {
    white-space: nowrap;
}
div.dts div.dts_loading {
    z-index: 1;
}
div.dts div.dts_label {
    position: absolute;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.5);
    text-align: right;
    border-radius: 3px;
    padding: 0.4em;
    z-index: 2;
    display: none;
}
div.dts div.dataTables_scrollBody {
    background: repeating-linear-gradient(
        45deg,
        #edeeff,
        #edeeff 10px,
        white 10px,
        white 20px
    );
}
div.dts div.dataTables_scrollBody table {
    background-color: white;
    z-index: 2;
}
div.dts div.dataTables_paginate,
div.dts div.dataTables_length {
    display: none;
}
div.DTS div.dataTables_scrollBody table {
    background-color: white;
}

table.dataTable > tbody > tr > .selected {
    background-color: rgba(13, 110, 253, 0.9);
    color: white;
}
table.dataTable > tbody > tr > td.select-checkbox,
table.dataTable > tbody > tr > th.select-checkbox {
    position: relative;
}
table.dataTable > tbody > tr > td.select-checkbox:before,
table.dataTable > tbody > tr > td.select-checkbox:after,
table.dataTable > tbody > tr > th.select-checkbox:before,
table.dataTable > tbody > tr > th.select-checkbox:after {
    display: block;
    position: absolute;
    top: 1.2em;
    left: 50%;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
}
table.dataTable > tbody > tr > td.select-checkbox:before,
table.dataTable > tbody > tr > th.select-checkbox:before {
    content: " ";
    margin-top: -5px;
    margin-left: -6px;
    border: 1px solid black;
    border-radius: 3px;
}
table.dataTable > tbody > tr.selected > td.select-checkbox:before,
table.dataTable > tbody > tr.selected > th.select-checkbox:before {
    border: 1px solid white;
}
table.dataTable > tbody > tr.selected > td.select-checkbox:after,
table.dataTable > tbody > tr.selected > th.select-checkbox:after {
    content: "✓";
    font-size: 20px;
    margin-top: -19px;
    margin-left: -6px;
    text-align: center;
    text-shadow: 1px 1px #b0bed9, -1px -1px #b0bed9, 1px -1px #b0bed9,
        -1px 1px #b0bed9;
}
table.dataTable.compact > tbody > tr > td.select-checkbox:before,
table.dataTable.compact > tbody > tr > th.select-checkbox:before {
    margin-top: -12px;
}
table.dataTable.compact > tbody > tr.selected > td.select-checkbox:after,
table.dataTable.compact > tbody > tr.selected > th.select-checkbox:after {
    margin-top: -16px;
}
div.dataTables_wrapper span.select-info,
div.dataTables_wrapper span.select-item {
    margin-left: 0.5em;
}
@media screen and (max-width: 640px) {
    div.dataTables_wrapper span.select-info,
    div.dataTables_wrapper span.select-item {
        margin-left: 0;
        display: block;
    }
}
table.dataTable.table-sm tbody td.select-checkbox::before {
    margin-top: -9px;
}
