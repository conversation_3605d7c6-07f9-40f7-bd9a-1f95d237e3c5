.loading_wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #fff;
    display: block;
    position: absolute;
  }

  .loader_logo {
    height: 80px;
    width: 80px;
    position: absolute;
    left: calc(50% - 50px);
    top: 38%;
  }

  .loader_logo img {
    height: 100%;
    width: 100%;
  }

  .loading {
    border: 3px solid rgba(102, 51, 153, 0.45);
    position: absolute;
    left: calc(50% - 40px);
    top: 50%;
    width: 55px;
    height: 55px;
    border-radius: 50%;
    border-top-color: #8b5cf6;
    animation: loader 1s ease-in-out infinite;
    -webkit-animation: loader 1s ease-in-out infinite;
  }

  @keyframes loader {
    to {
      -webkit-transform: rotate(360deg);
    }
  }

  @-webkit-keyframes loader {
    to {
      -webkit-transform: rotate(360deg);
    }
  }