"use strict";Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest=function(t){var e=this;if(!document.documentElement.contains(this))return null;do{if(e.matches(t))return e;e=e.parentElement}while(null!==e);return null})
/**
 * ChildNode.remove() polyfill
 * https://gomakethings.com/removing-an-element-from-the-dom-the-es6-way/
 * <AUTHOR>
 * @license MIT
 */,function(t){for(var e=0;e<t.length;e++)window[t[e]]&&!("remove"in window[t[e]].prototype)&&(window[t[e]].prototype.remove=function(){this.parentNode.removeChild(this)})}(["Element","CharacterData","DocumentType"]),function(){for(var t=0,e=["webkit","moz"],n=0;n<e.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[e[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[n]+"CancelAnimationFrame"]||window[e[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e){var n=(new Date).getTime(),i=Math.max(0,16-(n-t)),o=window.setTimeout((function(){e(n+i)}),i);return t=n+i,o}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(t){clearTimeout(t)})}(),[Element.prototype,Document.prototype,DocumentFragment.prototype].forEach((function(t){t.hasOwnProperty("prepend")||Object.defineProperty(t,"prepend",{configurable:!0,enumerable:!0,writable:!0,value:function(){var t=Array.prototype.slice.call(arguments),e=document.createDocumentFragment();t.forEach((function(t){var n=t instanceof Node;e.appendChild(n?t:document.createTextNode(String(t)))})),this.insertBefore(e,this.firstChild)}})})),window.ULUtilElementDataStore={},window.ULUtilElementDataStoreID=0,window.ULUtilDelegatedEventHandlers={};var ULUtil=function(){var t=[],e={sm:544,md:768,lg:1024,xl:1200},n=function(){var e=!1;window.addEventListener("resize",(function(){clearTimeout(e),e=setTimeout((function(){!function(){for(var e=0;e<t.length;e++)t[e].call()}()}),250)}))};return{init:function(t){t&&t.breakpoints&&(e=t.breakpoints),n()},addResizeHandler:function(e){t.push(e)},removeResizeHandler:function(e){for(var n=0;n<t.length;n++)e===t[n]&&delete t[n]},runResizeHandlers:function(){_runResizeHandlers()},resize:function(){if("function"==typeof Event)window.dispatchEvent(new Event("resize"));else{var t=window.document.createEvent("UIEvents");t.initUIEvent("resize",!0,!1,window,0),window.dispatchEvent(t)}},debounce:function(t,e){let n;return(...i)=>{n&&clearTimeout(n),n=setTimeout((()=>t(...i)),e>0?e:300)}},getURLParam:function(t){var e,n,i=window.location.search.substring(1).split("&");for(e=0;e<i.length;e++)if((n=i[e].split("="))[0]==t)return unescape(n[1]);return null},isMobile:function(){return this.getViewPort().width<=this.getBreakpoint("lg")},isDesktop:function(){return!ULUtil.isMobileDevice()},getViewPort:function(){var t=window,e="inner";return"innerWidth"in window||(e="client",t=document.documentElement||document.body),{width:t[e+"Width"],height:t[e+"Height"]}},isInResponsiveRange:function(t){var e=this.getViewPort().width;return"general"==t||("desktop"==t&&e>=this.getBreakpoint("lg")+1||("tablet"==t&&e>=this.getBreakpoint("md")+1&&e<this.getBreakpoint("lg")||("mobile"==t&&e<=this.getBreakpoint("md")||("desktop-and-tablet"==t&&e>=this.getBreakpoint("md")+1||("tablet-and-mobile"==t&&e<=this.getBreakpoint("lg")||"minimal-desktop-and-below"==t&&e<=this.getBreakpoint("xl"))))))},getUniqueID:function(t){return t+Math.floor(Math.random()*(new Date).getTime())},getBreakpoint:function(t){return e[t]},isset:function(t,e){var n;if(-1!==(e=e||"").indexOf("["))throw new Error("Unsupported object path notation.");e=e.split(".");do{if(void 0===t)return!1;if(n=e.shift(),!t.hasOwnProperty(n))return!1;t=t[n]}while(e.length);return!0},getHighestZindex:function(t){for(var e,n,i=ULUtil.get(t);i&&i!==document;){if(("absolute"===(e=ULUtil.css(i,"position"))||"relative"===e||"fixed"===e)&&(n=parseInt(ULUtil.css(i,"z-index")),!isNaN(n)&&0!==n))return n;i=i.parentNode}return null},hasFixedPositionedParent:function(t){for(;t&&t!==document;){if("fixed"===ULUtil.css(t,"position"))return!0;t=t.parentNode}return!1},sleep:function(t){for(var e=(new Date).getTime(),n=0;n<1e7&&!((new Date).getTime()-e>t);n++);},getRandomInt:function(t,e){return Math.floor(Math.random()*(e-t+1))+t},deepExtend:function(t){t=t||{};for(var e=1;e<arguments.length;e++){var n=arguments[e];if(n)for(var i in n)n.hasOwnProperty(i)&&("object"==typeof n[i]?t[i]=ULUtil.deepExtend(t[i],n[i]):t[i]=n[i])}return t},extend:function(t){t=t||{};for(var e=1;e<arguments.length;e++)if(arguments[e])for(var n in arguments[e])arguments[e].hasOwnProperty(n)&&(t[n]=arguments[e][n]);return t},get:function(t){var e;return t===document?document:t&&1===t.nodeType?t:(e=document.getElementById(t))?e:(e=document.getElementsByTagName(t))||(e=document.getElementsByClassName(t))?e[0]:null},getByID:function(t){return t&&1===t.nodeType?t:document.getElementById(t)},getByTag:function(t){var e;return(e=document.getElementsByTagName(t))?e[0]:null},getByClass:function(t){var e;return(e=document.getElementsByClassName(t))?e[0]:null},hasClasses:function(t,e){if(t){for(var n=e.split(" "),i=0;i<n.length;i++)if(0==ULUtil.hasClass(t,ULUtil.trim(n[i])))return!1;return!0}},hasClass:function(t,e){if(t)return t.classList?t.classList.contains(e):new RegExp("\\b"+e+"\\b").test(t.className)},addClass:function(t,e){if(t&&void 0!==e){var n=e.split(" ");if(t.classList)for(var i=0;i<n.length;i++)n[i]&&n[i].length>0&&t.classList.add(ULUtil.trim(n[i]));else if(!ULUtil.hasClass(t,e))for(var o=0;o<n.length;o++)t.className+=" "+ULUtil.trim(n[o])}},removeClass:function(t,e){if(t&&void 0!==e){var n=e.split(" ");if(t.classList)for(var i=0;i<n.length;i++)t.classList.remove(ULUtil.trim(n[i]));else if(ULUtil.hasClass(t,e))for(var o=0;o<n.length;o++)t.className=t.className.replace(new RegExp("\\b"+ULUtil.trim(n[o])+"\\b","g"),"")}},removeClassByPrefix:function(t,e){var n=new RegExp("\\b"+e+"[^ ]*[ ]?\\b","g");return t.className=t.className.replace(n,""),t},triggerCustomEvent:function(t,e,n){var i;window.CustomEvent?i=new CustomEvent(e,{detail:n}):(i=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,n),t.dispatchEvent(i)},triggerEvent:function(t,e){var n;if(t.ownerDocument)n=t.ownerDocument;else{if(9!=t.nodeType)throw new Error("Invalid node passed to fireEvent: "+t.id);n=t}if(t.dispatchEvent){var i="";switch(e){case"click":case"mouseenter":case"mouseleave":case"mousedown":case"mouseup":i="MouseEvents";break;case"focus":case"change":case"blur":case"select":i="HTMLEvents";break;default:throw"fireEvent: Couldn't find an event class for event '"+e+"'."}var o="change"!=e;(r=n.createEvent(i)).initEvent(e,o,!0),r.synthetic=!0,t.dispatchEvent(r,!0)}else if(t.fireEvent){var r;(r=n.createEventObject()).synthetic=!0,t.fireEvent("on"+e,r)}},index:function(t){for(var e=(t=ULUtil.get(t)).parentNode.children,n=0;n<e.length;n++)if(e[n]==t)return n},trim:function(t){return t.trim()},eventTriggered:function(t){return!!t.currentTarget.dataset.triggered||(t.currentTarget.dataset.triggered=!0,!1)},remove:function(t){t&&t.parentNode&&t.parentNode.removeChild(t)},find:function(t,e){if(t=ULUtil.get(t))return t.querySelector(e)},findAll:function(t,e){if(t=ULUtil.get(t))return t.querySelectorAll(e)},insertAfter:function(t,e){return e.parentNode.insertBefore(t,e.nextSibling)},parents:function(t,e){Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(t){for(var e=(this.document||this.ownerDocument).querySelectorAll(t),n=e.length;--n>=0&&e.item(n)!==this;);return n>-1});for(var n=[];t&&t!==document;t=t.parentNode)e?t.matches(e)&&n.push(t):n.push(t);return n},children:function(t,e,n){if(t&&t.childNodes){for(var i=[],o=0,r=t.childNodes.length;o<r;++o)1==t.childNodes[o].nodeType&&ULUtil.matches(t.childNodes[o],e,n)&&i.push(t.childNodes[o]);return i}},child:function(t,e,n){var i=ULUtil.children(t,e,n);return i?i[0]:null},matches:function(t,e,n){var i=Element.prototype,o=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(t){return-1!==[].indexOf.call(document.querySelectorAll(t),this)};return!(!t||!t.tagName)&&o.call(t,e)},data:function(t){return t=ULUtil.get(t),{set:function(e,n){void 0!==t&&(void 0===t.customDataTag&&(window.ULUtilElementDataStoreID++,t.customDataTag=window.ULUtilElementDataStoreID),void 0===window.ULUtilElementDataStore[t.customDataTag]&&(window.ULUtilElementDataStore[t.customDataTag]={}),window.ULUtilElementDataStore[t.customDataTag][e]=n)},get:function(e){if(void 0!==t)return void 0===t.customDataTag?null:this.has(e)?window.ULUtilElementDataStore[t.customDataTag][e]:null},has:function(e){return void 0!==t&&(void 0!==t.customDataTag&&!(!window.ULUtilElementDataStore[t.customDataTag]||!window.ULUtilElementDataStore[t.customDataTag][e]))},remove:function(e){t&&this.has(e)&&delete window.ULUtilElementDataStore[t.customDataTag][e]}}},outerWidth:function(t,e){var n;return!0===e?(n=parseFloat(t.offsetWidth),n+=parseFloat(ULUtil.css(t,"margin-left"))+parseFloat(ULUtil.css(t,"margin-right")),parseFloat(n)):n=parseFloat(t.offsetWidth)},offset:function(t){var e,n;if(t=ULUtil.get(t))return t.getClientRects().length?(e=t.getBoundingClientRect(),n=t.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}},height:function(t){return ULUtil.css(t,"height")},visible:function(t){return!(0===t.offsetWidth&&0===t.offsetHeight)},attr:function(t,e,n){if(null!=(t=ULUtil.get(t)))return void 0===n?t.getAttribute(e):void t.setAttribute(e,n)},hasAttr:function(t,e){if(null!=(t=ULUtil.get(t)))return!!t.getAttribute(e)},removeAttr:function(t,e){null!=(t=ULUtil.get(t))&&t.removeAttribute(e)},animate:function(t,e,n,i,o,r){var a={};if(a.linear=function(t,e,n,i){return n*t/i+e},o=a.linear,"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"function"==typeof i){"function"!=typeof r&&(r=function(){});var l=window.requestAnimationFrame||function(t){window.setTimeout(t,20)},s=e-t;i(t);var d=window.performance&&window.performance.now?window.performance.now():+new Date;l((function a(c){var u=(c||+new Date)-d;u>=0&&i(o(u,t,s,n)),u>=0&&u>=n?(i(e),r()):l(a)}))}},actualCss:function(t,e,n){var i,o="";if((t=ULUtil.get(t))instanceof HTMLElement!=!1)return t.getAttribute("ul-hidden-"+e)&&!1!==n?parseFloat(t.getAttribute("ul-hidden-"+e)):(o=t.style.cssText,t.style.cssText="position: absolute; visibility: hidden; display: block;","width"==e?i=t.offsetWidth:"height"==e&&(i=t.offsetHeight),t.style.cssText=o,t.setAttribute("ul-hidden-"+e,i),parseFloat(i))},actualHeight:function(t,e){return ULUtil.actualCss(t,"height",e)},actualWidth:function(t,e){return ULUtil.actualCss(t,"width",e)},getScroll:function(t,e){return e="scroll"+e,t==window||t==document?self["scrollTop"==e?"pageYOffset":"pageXOffset"]||browserSupportsBoxModel&&document.documentElement[e]||document.body[e]:t[e]},css:function(t,e,n){if(t=ULUtil.get(t))if(void 0!==n)t.style[e]=n;else{var i=(t.ownerDocument||document).defaultView;if(i&&i.getComputedStyle)return e=e.replace(/([A-Z])/g,"-$1").toLowerCase(),i.getComputedStyle(t,null).getPropertyValue(e);if(t.currentStyle)return e=e.replace(/\-(\w)/g,(function(t,e){return e.toUpperCase()})),n=t.currentStyle[e],/^\d+(em|pt|%|ex)?$/i.test(n)?function(e){var n=t.style.left,i=t.runtimeStyle.left;return t.runtimeStyle.left=t.currentStyle.left,t.style.left=e||0,e=t.style.pixelLeft+"px",t.style.left=n,t.runtimeStyle.left=i,e}(n):n}},slide:function(t,e,n,i,o){if(!(!t||"up"==e&&!1===ULUtil.visible(t)||"down"==e&&!0===ULUtil.visible(t))){n=n||600;var r=ULUtil.actualHeight(t),a=!1,l=!1;ULUtil.css(t,"padding-top")&&!0!==ULUtil.data(t).has("slide-padding-top")&&ULUtil.data(t).set("slide-padding-top",ULUtil.css(t,"padding-top")),ULUtil.css(t,"padding-bottom")&&!0!==ULUtil.data(t).has("slide-padding-bottom")&&ULUtil.data(t).set("slide-padding-bottom",ULUtil.css(t,"padding-bottom")),ULUtil.data(t).has("slide-padding-top")&&(a=parseInt(ULUtil.data(t).get("slide-padding-top"))),ULUtil.data(t).has("slide-padding-bottom")&&(l=parseInt(ULUtil.data(t).get("slide-padding-bottom"))),"up"==e?(t.style.cssText="display: block; overflow: hidden;",a&&ULUtil.animate(0,a,n,(function(e){t.style.paddingTop=a-e+"px"}),"linear"),l&&ULUtil.animate(0,l,n,(function(e){t.style.paddingBottom=l-e+"px"}),"linear"),ULUtil.animate(0,r,n,(function(e){t.style.height=r-e+"px"}),"linear",(function(){i(),t.style.height="",t.style.display="none"}))):"down"==e&&(t.style.cssText="display: block; overflow: hidden;",a&&ULUtil.animate(0,a,n,(function(e){t.style.paddingTop=e+"px"}),"linear",(function(){t.style.paddingTop=""})),l&&ULUtil.animate(0,l,n,(function(e){t.style.paddingBottom=e+"px"}),"linear",(function(){t.style.paddingBottom=""})),ULUtil.animate(0,r,n,(function(e){t.style.height=e+"px"}),"linear",(function(){i(),t.style.height="",t.style.display="",t.style.overflow=""})))}},slideUp:function(t,e,n){ULUtil.slide(t,"up",e,n)},slideDown:function(t,e,n){ULUtil.slide(t,"down",e,n)},show:function(t,e){void 0!==t&&(t.style.display=e||"block")},hide:function(t){void 0!==t&&(t.style.display="none")},addEvent:function(t,e,n,i){void 0!==(t=ULUtil.get(t))&&t.addEventListener(e,n)},removeEvent:function(t,e,n){(t=ULUtil.get(t)).removeEventListener(e,n)},on:function(t,e,n,i){if(e){var o=ULUtil.getUniqueID("event");return window.ULUtilDelegatedEventHandlers[o]=function(n){for(var o=t.querySelectorAll(e),r=n.target;r&&r!==t;){for(var a=0,l=o.length;a<l;a++)r===o[a]&&i.call(r,n);r=r.parentNode}},ULUtil.addEvent(t,n,window.ULUtilDelegatedEventHandlers[o]),o}},off:function(t,e,n){t&&window.ULUtilDelegatedEventHandlers[n]&&(ULUtil.removeEvent(t,e,window.ULUtilDelegatedEventHandlers[n]),delete window.ULUtilDelegatedEventHandlers[n])},one:function(t,e,n){(t=ULUtil.get(t)).addEventListener(e,(function t(e){return e.target&&e.target.removeEventListener&&e.target.removeEventListener(e.type,t),n(e)}))},hash:function(t){var e,n=0;if(0===t.length)return n;for(e=0;e<t.length;e++)n=(n<<5)-n+t.charCodeAt(e),n|=0;return n},animateClass:function(t,e,n){var i,o={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd",msAnimation:"msAnimationEnd"};for(var r in o)void 0!==t.style[r]&&(i=o[r]);ULUtil.addClass(t,"animated "+e),ULUtil.one(t,i,(function(){ULUtil.removeClass(t,"animated "+e)})),n&&ULUtil.one(t,i,n)},transitionEnd:function(t,e){var n,i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"mozTransitionEnd",WebkitTransition:"webkitTransitionEnd",msTransition:"msTransitionEnd"};for(var o in i)void 0!==t.style[o]&&(n=i[o]);ULUtil.one(t,n,e)},animationEnd:function(t,e){var n,i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd",msAnimation:"msAnimationEnd"};for(var o in i)void 0!==t.style[o]&&(n=i[o]);ULUtil.one(t,n,e)},animateDelay:function(t,e){for(var n=["webkit-","moz-","ms-","o-",""],i=0;i<n.length;i++)ULUtil.css(t,n[i]+"animation-delay",e)},animateDuration:function(t,e){for(var n=["webkit-","moz-","ms-","o-",""],i=0;i<n.length;i++)ULUtil.css(t,n[i]+"animation-duration",e)},scrollTo:function(t,e,n){n=n||500;var i,o,r=(t=ULUtil.get(t))?ULUtil.offset(t).top:0,a=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;r>a?(i=r,o=a):(i=a,o=r),e&&(o+=e),ULUtil.animate(i,o,n,(function(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}))},scrollTop:function(t,e){ULUtil.scrollTo(null,t,e)},isArray:function(t){return t&&Array.isArray(t)},ready:function(t){(document.attachEvent?"complete"===document.readyState:"loading"!==document.readyState)?t():document.addEventListener("DOMContentLoaded",t)},isEmpty:function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0},numberString:function(t){for(var e=(t+="").split("."),n=e[0],i=e.length>1?"."+e[1]:"",o=/(\d+)(\d{3})/;o.test(n);)n=n.replace(o,"$1,$2");return n+i},detectIE:function(){var t=window.navigator.userAgent,e=t.indexOf("MSIE ");if(e>0)return parseInt(t.substring(e+5,t.indexOf(".",e)),10);if(t.indexOf("Trident/")>0){var n=t.indexOf("rv:");return parseInt(t.substring(n+3,t.indexOf(".",n)),10)}var i=t.indexOf("Edge/");return i>0&&parseInt(t.substring(i+5,t.indexOf(".",i)),10)},isRTL:function(){return"rtl"==ULUtil.attr(ULUtil.get("html"),"direction")},scrollInit:function(t,e){function n(){var n,i;if(i=e.height instanceof Function?parseInt(e.height.call()):parseInt(e.height),(e.mobileNativeScroll||e.disableForMobile)&&ULUtil.isInResponsiveRange("tablet-and-mobile"))(n=ULUtil.data(t).get("ps"))?(e.resetHeightOnDestroy?ULUtil.css(t,"height","auto"):(ULUtil.css(t,"overflow","auto"),i>0&&ULUtil.css(t,"height",i+"px")),n.destroy(),n=ULUtil.data(t).remove("ps")):i>0&&(ULUtil.css(t,"overflow","auto"),ULUtil.css(t,"height",i+"px"));else if(i>0&&ULUtil.css(t,"height",i+"px"),e.desktopNativeScroll)ULUtil.css(t,"overflow","auto");else{ULUtil.css(t,"overflow","hidden"),(n=ULUtil.data(t).get("ps"))?n.update():(ULUtil.addClass(t,"ul-scroll"),n=new PerfectScrollbar(t,{wheelSpeed:.5,swipeEasing:!0,wheelPropagation:!1!==e.windowScroll,minScrollbarLength:40,maxScrollbarLength:300,suppressScrollX:"true"!=ULUtil.attr(t,"data-scroll-x")}),ULUtil.data(t).set("ps",n));var o=ULUtil.attr(t,"id");if(!0===e.rememberPosition&&Cookies&&o){if(Cookies.get(o)){var r=parseInt(Cookies.get(o));r>0&&(t.scrollTop=r)}t.addEventListener("ps-scroll-y",(function(){Cookies.set(o,t.scrollTop)}))}}}t&&(n(),e.handleWindowResize&&ULUtil.addResizeHandler((function(){n()})))},scrollUpdate:function(t){var e=ULUtil.data(t).get("ps");e&&e.update()},scrollUpdateAll:function(t){for(var e=ULUtil.findAll(t,".ps"),n=0,i=e.length;n<i;n++)ULUtil.scrollerUpdate(e[n])},scrollDestroy:function(t){var e=ULUtil.data(t).get("ps");e&&(e.destroy(),e=ULUtil.data(t).remove("ps"))},setHTML:function(t,e){ULUtil.get(t)&&(ULUtil.get(t).innerHTML=e)},getHTML:function(t){if(ULUtil.get(t))return ULUtil.get(t).innerHTML},getDocumentHeight:function(){var t=document.body,e=document.documentElement;return Math.max(t.scrollHeight,t.offsetHeight,e.clientHeight,e.scrollHeight,e.offsetHeight)},getScrollTop:function(){return(document.scrollingElement||document.documentElement).scrollTop}}}();window.ULUtil=ULUtil,"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=ULUtil),ULUtil.ready((function(){ULUtil.init()})),window.onload=function(){ULUtil.removeClass(ULUtil.get("body"),"ul-page--loading")};