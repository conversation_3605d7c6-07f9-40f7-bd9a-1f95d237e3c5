var SidebarPanel=(sidebarPanel=function(){var e=document.querySelector(".ul-sidebar-panel-overlay"),a=document.querySelectorAll("[data-sidebar-panel]"),l=document.querySelectorAll(".ul-sidebar-panel");a.forEach((a=>{a.addEventListener("click",(function(l){var n=document.getElementById(ULUtil.attr(a,"data-sidebar-panel"));n&&(ULUtil.hasClass(n,"open")?ULUtil.removeClass(n,"open"):ULUtil.addClass(n,"open")),e&&(ULUtil.hasClass(e,"open")?ULUtil.removeClass(e,"open"):ULUtil.addClass(e,"open"))}))})),e&&e.addEventListener("click",(function(e){l.forEach((e=>{ULUtil.removeClass(e,"open")})),ULUtil.removeClass(e.target,"open")})),document.querySelectorAll(".ul-sidebar-panel-close").forEach((a=>{a.addEventListener("click",(function(a){var l=a.target.closest(".ul-sidebar-panel");ULUtil.removeClass(l,"open"),ULUtil.removeClass(e,"open")}))}))},{init:function(){sidebarPanel()}});"loading"!==document.readyState?SidebarPanel.init():document.addEventListener("DOMContentLoaded",SidebarPanel.init);