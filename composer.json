{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-dompdf": "^2.2.0", "doctrine/dbal": "^3.0", "ezyang/htmlpurifier": "^4.16", "gumlet/php-image-resize": "2.0.1", "guzzlehttp/guzzle": "^7.0.1", "infobip/infobip-api-php-client": "3.2.0", "intervention/image": "^2.5", "khaled.alshamaa/ar-php": "^6.3", "laravel/framework": "^10.0", "laravel/passport": "^11.0", "laravel/tinker": "^2.0", "laravel/ui": "^4.0", "lcobucci/jwt": "^4.3", "maatwebsite/excel": "^3.0.1", "macellan/laravel-zip": "^1.0", "nesbot/carbon": "^2.38", "nwidart/laravel-modules": "^8.3", "stripe/stripe-php": "^7.76", "twilio/sdk": "^6.22", "kitloong/laravel-migrations-generator": "^6.0", "yajra/laravel-datatables-oracle": "^10.0"}, "require-dev": {"spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.16", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/"}, "classmap": ["database/seeders", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}