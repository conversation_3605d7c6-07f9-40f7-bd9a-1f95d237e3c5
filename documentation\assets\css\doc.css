/*
  Done Design System by UI Lib
*/
/*
  Color Inventory
*/
/* 
  Color Design Tokens 
*/
/*
$opacity-map: (
  blue: (
    100: #f31312,
    .....,
    900: #f32332
  ),
  red: (
    100: #f31312,
    .....,
    900: #f32332
  )
)
*/
.opacity {
  background: white;
}

body {
  font-family: "Roboto", sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.01875rem;
  background: #fff;
  color: #1a1a1a;
  overflow-x: hidden;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

[tabindex="-1"]:focus {
  outline: none;
}

img {
  max-width: 100%;
}

a {
  color: #8b5cf6;
  text-decoration: none;
}
a:hover {
  color: #4d2673;
  text-decoration: underline;
}

select,
button,
textarea,
input {
  vertical-align: baseline;
}

div {
  box-sizing: border-box;
}

html[dir="rtl"],
html[dir="ltr"],
body[dir="rtl"],
body[dir="ltr"] {
  unicode-bidi: embed;
}

bdo[dir="rtl"] {
  direction: rtl;
  unicode-bidi: bidi-override;
}

bdo[dir="ltr"] {
  direction: ltr;
  unicode-bidi: bidi-override;
}

.fa,
.fas {
  font-weight: 900 !important;
}

svg.feather {
  height: 1rem;
  width: 1rem;
  vertical-align: text-top;
}

button,
input,
optgroup,
select,
textarea {
  font-family: "Roboto", sans-serif;
}

p {
  font-weight: 300;
  line-height: 1.7;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5 h6,
.h6 {
  font-weight: 400;
  color: black;
  line-height: 1.25;
}

h1,
.h1 {
  font-size: 2.25rem;
  margin-bottom: 0.5rem;
}
h1 small,
.h1 small {
  font-weight: 700;
  text-transform: uppercase;
  opacity: 0.8;
}

h2,
.h2 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

h3,
.h3 {
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
  line-height: 1.4em;
}

h4,
.h4 {
  font-size: 1.5rem;
  line-height: 1.45em;
  margin-bottom: 0.5rem;
}

h5,
.h5 {
  font-size: 1.25rem;
  line-height: 1.4em;
  margin-bottom: 0.5rem;
}

h6,
.h6 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.display-1 {
  font-size: 3.25rem;
  font-weight: 500;
}

.display-2 {
  font-size: 2.75rem;
  font-weight: 500;
}

.display-3 {
  font-size: 2.25rem;
  font-weight: 500;
}

.display-4 {
  font-size: 1.75rem;
  font-weight: 500;
}

.heading {
  font-size: 0.9375rem;
  text-transform: uppercase;
  letter-spacing: 0.03125rem;
}

.heading-title {
  font-size: 1.375rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.03125rem;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.blockquote {
  font-size: 1rem;
}
.blockquote-footer {
  font-weight: 700;
  color: #525252;
}
.blockquote.blockquote-primary {
  color: #8b5cf6 !important;
}
.blockquote.blockquote-primary .blockquote-footer {
  color: #7038a8;
}
.blockquote.blockquote-secondary {
  color: #1f1f1f !important;
}
.blockquote.blockquote-secondary .blockquote-footer {
  color: #292929;
}
.blockquote.blockquote-success {
  color: #4caf50 !important;
}
.blockquote.blockquote-success .blockquote-footer {
  color: #59b75c;
}
.blockquote.blockquote-info {
  color: #003473 !important;
}
.blockquote.blockquote-info .blockquote-footer {
  color: #003d87;
}
.blockquote.blockquote-warning {
  color: #e97d23 !important;
}
.blockquote.blockquote-warning .blockquote-footer {
  color: #eb8836;
}
.blockquote.blockquote-danger {
  color: #f44336 !important;
}
.blockquote.blockquote-danger .blockquote-footer {
  color: #f55549;
}
.blockquote.blockquote-light {
  color: #666666 !important;
}
.blockquote.blockquote-light .blockquote-footer {
  color: #707070;
}
.blockquote.blockquote-dark {
  color: #0a0a0a !important;
}
.blockquote.blockquote-dark .blockquote-footer {
  color: #141414;
}

.text-10 {
  font-size: 10px;
}

.text-11 {
  font-size: 11px;
}

.text-12 {
  font-size: 12px;
}

.text-13 {
  font-size: 13px;
}

.text-14 {
  font-size: 14px;
}

.text-15 {
  font-size: 15px;
}

.text-16 {
  font-size: 16px;
}

.text-17 {
  font-size: 17px;
}

.text-18 {
  font-size: 18px;
}

.text-19 {
  font-size: 19px;
}

.text-20 {
  font-size: 20px;
}

.text-21 {
  font-size: 21px;
}

.text-22 {
  font-size: 22px;
}

.text-23 {
  font-size: 23px;
}

.text-24 {
  font-size: 24px;
}

.text-25 {
  font-size: 25px;
}

.text-26 {
  font-size: 26px;
}

.text-27 {
  font-size: 27px;
}

.text-28 {
  font-size: 28px;
}

.text-29 {
  font-size: 29px;
}

.text-30 {
  font-size: 30px;
}

.text-31 {
  font-size: 31px;
}

.text-32 {
  font-size: 32px;
}

.text-33 {
  font-size: 33px;
}

.text-34 {
  font-size: 34px;
}

.text-35 {
  font-size: 35px;
}

.text-36 {
  font-size: 36px;
}

.text-37 {
  font-size: 37px;
}

.text-38 {
  font-size: 38px;
}

.text-39 {
  font-size: 39px;
}

.text-40 {
  font-size: 40px;
}

.text-41 {
  font-size: 41px;
}

.text-42 {
  font-size: 42px;
}

.text-43 {
  font-size: 43px;
}

.text-44 {
  font-size: 44px;
}

.text-45 {
  font-size: 45px;
}

.text-46 {
  font-size: 46px;
}

.text-47 {
  font-size: 47px;
}

.text-48 {
  font-size: 48px;
}

.text-49 {
  font-size: 49px;
}

.text-50 {
  font-size: 50px;
}

.text-51 {
  font-size: 51px;
}

.text-52 {
  font-size: 52px;
}

.text-53 {
  font-size: 53px;
}

.text-54 {
  font-size: 54px;
}

.text-55 {
  font-size: 55px;
}

.text-56 {
  font-size: 56px;
}

.text-57 {
  font-size: 57px;
}

.text-58 {
  font-size: 58px;
}

.text-59 {
  font-size: 59px;
}

.text-60 {
  font-size: 60px;
}

.text-61 {
  font-size: 61px;
}

.text-62 {
  font-size: 62px;
}

.text-63 {
  font-size: 63px;
}

.text-64 {
  font-size: 64px;
}

.text-65 {
  font-size: 65px;
}

.text-66 {
  font-size: 66px;
}

.text-67 {
  font-size: 67px;
}

.text-68 {
  font-size: 68px;
}

.text-69 {
  font-size: 69px;
}

.text-70 {
  font-size: 70px;
}

.text-71 {
  font-size: 71px;
}

.text-72 {
  font-size: 72px;
}

.text-73 {
  font-size: 73px;
}

.text-74 {
  font-size: 74px;
}

.text-75 {
  font-size: 75px;
}

.text-76 {
  font-size: 76px;
}

.text-77 {
  font-size: 77px;
}

.text-78 {
  font-size: 78px;
}

.text-muted {
  color: #525252 !important;
}

small {
  font-size: 80%;
}

.text-small {
  font-size: 0.8125rem;
}

.font-weight-semi {
  font-weight: 500;
}

.h-0px {
  height: 0px;
}

.h-100px {
  height: 100px;
}

.h-200px {
  height: 200px;
}

.h-300px {
  height: 300px;
}

.h-400px {
  height: 400px;
}

.h-500px {
  height: 500px;
}

.h-600px {
  height: 600px;
}

.h-700px {
  height: 700px;
}

.h-800px {
  height: 800px;
}

.h-900px {
  height: 900px;
}

.h-1000px {
  height: 1000px;
}

.h-100vh,
[h-100vh] {
  min-height: 100vh;
}

[w-0],
.w-0 {
  width: 0% !important;
}

[w-10],
.w-10 {
  width: 10% !important;
}

[w-20],
.w-20 {
  width: 20% !important;
}

[w-30],
.w-30 {
  width: 30% !important;
}

[w-40],
.w-40 {
  width: 40% !important;
}

[w-50],
.w-50 {
  width: 50% !important;
}

[w-60],
.w-60 {
  width: 60% !important;
}

[w-70],
.w-70 {
  width: 70% !important;
}

[w-80],
.w-80 {
  width: 80% !important;
}

[w-90],
.w-90 {
  width: 90% !important;
}

[w-100],
.w-100 {
  width: 100% !important;
}

@media (max-width: 767px) {
  .w-sm-100 {
    width: 100% !important;
  }
}

.gray-100 {
  background-color: #c2c2c2;
}

.gray-200 {
  background-color: #f9f9f9;
}

.gray-300 {
  background-color: #999999;
}

.gray-400 {
  background-color: #7a7a7a;
}

.gray-500 {
  background-color: #666666;
}

.gray-600 {
  background-color: #525252;
}

.gray-700 {
  background-color: #333333;
}

.gray-800 {
  background-color: #1f1f1f;
}

.gray-900 {
  background-color: #0a0a0a;
}

.black {
  background-color: black;
}

.text-gray-100 {
  color: #c2c2c2;
}

.text-gray-200 {
  color: #f9f9f9;
}

.text-gray-300 {
  color: #999999;
}

.text-gray-400 {
  color: #7a7a7a;
}

.text-gray-500 {
  color: #666666;
}

.text-gray-600 {
  color: #525252;
}

.text-gray-700 {
  color: #333333;
}

.text-gray-800 {
  color: #1f1f1f;
}

.text-gray-900 {
  color: #0a0a0a;
}

.text-black {
  color: black;
}

.bg-primary {
  background-color: #8b5cf6 !important;
}

.text-primary {
  color: #8b5cf6 !important;
}

.bg-secondary {
  background-color: #1f1f1f !important;
}

.text-secondary {
  color: #1f1f1f !important;
}

.bg-success {
  background-color: #4caf50 !important;
}

.text-success {
  color: #4caf50 !important;
}

.bg-info {
  background-color: #003473 !important;
}

.text-info {
  color: #003473 !important;
}

.bg-warning {
  background-color: #e97d23 !important;
}

.text-warning {
  color: #e97d23 !important;
}

.bg-danger {
  background-color: #f44336 !important;
}

.text-danger {
  color: #f44336 !important;
}

.bg-light {
  background-color: #666666 !important;
}

.text-light {
  color: #666666 !important;
}

.bg-dark {
  background-color: #0a0a0a !important;
}

.text-dark {
  color: #0a0a0a !important;
}

.blue,
.bg-blue {
  background-color: #003473;
}

.text-blue {
  color: #003473;
}

.midnight-blue,
.bg-midnight-blue {
  background-color: #0c0c3c;
}

.text-midnight-blue {
  color: #0c0c3c;
}

.indigo,
.bg-indigo {
  background-color: #3f51b5;
}

.text-indigo {
  color: #3f51b5;
}

.dark-purple,
.bg-dark-purple {
  background-color: #322740;
}

.text-dark-purple {
  color: #322740;
}

.purple,
.bg-purple {
  background-color: #8b5cf6;
}

.text-purple {
  color: #8b5cf6;
}

.pink,
.bg-pink {
  background-color: #cb3066;
}

.text-pink {
  color: #cb3066;
}

.red,
.bg-red {
  background-color: #f44336;
}

.text-red {
  color: #f44336;
}

.orange,
.bg-orange {
  background-color: #e97d23;
}

.text-orange {
  color: #e97d23;
}

.yellow,
.bg-yellow {
  background-color: #ffc107;
}

.text-yellow {
  color: #ffc107;
}

.green,
.bg-green {
  background-color: #4caf50;
}

.text-green {
  color: #4caf50;
}

.teal,
.bg-teal {
  background-color: #20c997;
}

.text-teal {
  color: #20c997;
}

.cyan,
.bg-cyan {
  background-color: #9c27b0;
}

.text-cyan {
  color: #9c27b0;
}

.gray,
.bg-gray {
  background-color: #2d2d33;
}

.text-gray {
  color: #2d2d33;
}

.slate-gray,
.bg-slate-gray {
  background-color: #405365;
}

.text-slate-gray {
  color: #405365;
}

.m-xxxs {
  margin: 0.0625rem !important;
}

.mt-xxxs {
  margin-top: 0.0625rem !important;
}

.mr-xxxs {
  margin-right: 0.0625rem !important;
}

.mb-xxxs {
  margin-bottom: 0.0625rem !important;
}

.ml-xxxs {
  margin-left: 0.0625rem !important;
}

.mx-xxxs {
  margin-left: 0.0625rem !important;
  margin-right: 0.0625rem !important;
}

.my-xxxs {
  margin-top: 0.0625rem !important;
  margin-bottom: 0.0625rem !important;
}

.p-xxxs {
  padding: 0.0625rem !important;
}

.pt-xxxs {
  padding-top: 0.0625rem !important;
}

.pr-xxxs {
  padding-right: 0.0625rem !important;
}

.pb-xxxs {
  padding-bottom: 0.0625rem !important;
}

.pl-xxxs {
  padding-left: 0.0625rem !important;
}

.px-xxxs {
  padding-left: 0.0625rem !important;
  padding-right: 0.0625rem !important;
}

.py-xxxs {
  padding-top: 0.0625rem !important;
  padding-bottom: 0.0625rem !important;
}

.m-xxs {
  margin: 0.125rem !important;
}

.mt-xxs {
  margin-top: 0.125rem !important;
}

.mr-xxs {
  margin-right: 0.125rem !important;
}

.mb-xxs {
  margin-bottom: 0.125rem !important;
}

.ml-xxs {
  margin-left: 0.125rem !important;
}

.mx-xxs {
  margin-left: 0.125rem !important;
  margin-right: 0.125rem !important;
}

.my-xxs {
  margin-top: 0.125rem !important;
  margin-bottom: 0.125rem !important;
}

.p-xxs {
  padding: 0.125rem !important;
}

.pt-xxs {
  padding-top: 0.125rem !important;
}

.pr-xxs {
  padding-right: 0.125rem !important;
}

.pb-xxs {
  padding-bottom: 0.125rem !important;
}

.pl-xxs {
  padding-left: 0.125rem !important;
}

.px-xxs {
  padding-left: 0.125rem !important;
  padding-right: 0.125rem !important;
}

.py-xxs {
  padding-top: 0.125rem !important;
  padding-bottom: 0.125rem !important;
}

.m-xs {
  margin: 0.25rem !important;
}

.mt-xs {
  margin-top: 0.25rem !important;
}

.mr-xs {
  margin-right: 0.25rem !important;
}

.mb-xs {
  margin-bottom: 0.25rem !important;
}

.ml-xs {
  margin-left: 0.25rem !important;
}

.mx-xs {
  margin-left: 0.25rem !important;
  margin-right: 0.25rem !important;
}

.my-xs {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.p-xs {
  padding: 0.25rem !important;
}

.pt-xs {
  padding-top: 0.25rem !important;
}

.pr-xs {
  padding-right: 0.25rem !important;
}

.pb-xs {
  padding-bottom: 0.25rem !important;
}

.pl-xs {
  padding-left: 0.25rem !important;
}

.px-xs {
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}

.py-xs {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.m-sm {
  margin: 0.5rem !important;
}

.mt-sm {
  margin-top: 0.5rem !important;
}

.mr-sm {
  margin-right: 0.5rem !important;
}

.mb-sm {
  margin-bottom: 0.5rem !important;
}

.ml-sm {
  margin-left: 0.5rem !important;
}

.mx-sm {
  margin-left: 0.5rem !important;
  margin-right: 0.5rem !important;
}

.my-sm {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.p-sm {
  padding: 0.5rem !important;
}

.pt-sm {
  padding-top: 0.5rem !important;
}

.pr-sm {
  padding-right: 0.5rem !important;
}

.pb-sm {
  padding-bottom: 0.5rem !important;
}

.pl-sm {
  padding-left: 0.5rem !important;
}

.px-sm {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.py-sm {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.m-md {
  margin: 1rem !important;
}

.mt-md {
  margin-top: 1rem !important;
}

.mr-md {
  margin-right: 1rem !important;
}

.mb-md {
  margin-bottom: 1rem !important;
}

.ml-md {
  margin-left: 1rem !important;
}

.mx-md {
  margin-left: 1rem !important;
  margin-right: 1rem !important;
}

.my-md {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.p-md {
  padding: 1rem !important;
}

.pt-md {
  padding-top: 1rem !important;
}

.pr-md {
  padding-right: 1rem !important;
}

.pb-md {
  padding-bottom: 1rem !important;
}

.pl-md {
  padding-left: 1rem !important;
}

.px-md {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.py-md {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.m-lg {
  margin: 1.5rem !important;
}

.mt-lg {
  margin-top: 1.5rem !important;
}

.mr-lg {
  margin-right: 1.5rem !important;
}

.mb-lg {
  margin-bottom: 1.5rem !important;
}

.ml-lg {
  margin-left: 1.5rem !important;
}

.mx-lg {
  margin-left: 1.5rem !important;
  margin-right: 1.5rem !important;
}

.my-lg {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.p-lg {
  padding: 1.5rem !important;
}

.pt-lg {
  padding-top: 1.5rem !important;
}

.pr-lg {
  padding-right: 1.5rem !important;
}

.pb-lg {
  padding-bottom: 1.5rem !important;
}

.pl-lg {
  padding-left: 1.5rem !important;
}

.px-lg {
  padding-left: 1.5rem !important;
  padding-right: 1.5rem !important;
}

.py-lg {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.m-xl {
  margin: 2rem !important;
}

.mt-xl {
  margin-top: 2rem !important;
}

.mr-xl {
  margin-right: 2rem !important;
}

.mb-xl {
  margin-bottom: 2rem !important;
}

.ml-xl {
  margin-left: 2rem !important;
}

.mx-xl {
  margin-left: 2rem !important;
  margin-right: 2rem !important;
}

.my-xl {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

.p-xl {
  padding: 2rem !important;
}

.pt-xl {
  padding-top: 2rem !important;
}

.pr-xl {
  padding-right: 2rem !important;
}

.pb-xl {
  padding-bottom: 2rem !important;
}

.pl-xl {
  padding-left: 2rem !important;
}

.px-xl {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

.py-xl {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}

.m-xxl {
  margin: 2.5rem !important;
}

.mt-xxl {
  margin-top: 2.5rem !important;
}

.mr-xxl {
  margin-right: 2.5rem !important;
}

.mb-xxl {
  margin-bottom: 2.5rem !important;
}

.ml-xxl {
  margin-left: 2.5rem !important;
}

.mx-xxl {
  margin-left: 2.5rem !important;
  margin-right: 2.5rem !important;
}

.my-xxl {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.p-xxl {
  padding: 2.5rem !important;
}

.pt-xxl {
  padding-top: 2.5rem !important;
}

.pr-xxl {
  padding-right: 2.5rem !important;
}

.pb-xxl {
  padding-bottom: 2.5rem !important;
}

.pl-xxl {
  padding-left: 2.5rem !important;
}

.px-xxl {
  padding-left: 2.5rem !important;
  padding-right: 2.5rem !important;
}

.py-xxl {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.m-xxxl {
  margin: 3rem !important;
}

.mt-xxxl {
  margin-top: 3rem !important;
}

.mr-xxxl {
  margin-right: 3rem !important;
}

.mb-xxxl {
  margin-bottom: 3rem !important;
}

.ml-xxxl {
  margin-left: 3rem !important;
}

.mx-xxxl {
  margin-left: 3rem !important;
  margin-right: 3rem !important;
}

.my-xxxl {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.p-xxxl {
  padding: 3rem !important;
}

.pt-xxxl {
  padding-top: 3rem !important;
}

.pr-xxxl {
  padding-right: 3rem !important;
}

.pb-xxxl {
  padding-bottom: 3rem !important;
}

.pl-xxxl {
  padding-left: 3rem !important;
}

.px-xxxl {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

.py-xxxl {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.border-primary {
  border-color: #8b5cf6 !important;
}

.border-secondary {
  border-color: #1f1f1f !important;
}

.border-success {
  border-color: #4caf50 !important;
}

.border-info {
  border-color: #003473 !important;
}

.border-warning {
  border-color: #e97d23 !important;
}

.border-danger {
  border-color: #f44336 !important;
}

.border-light {
  border-color: #666666 !important;
}

.border-dark {
  border-color: #0a0a0a !important;
}

.shadow-1dp {
  box-shadow: 0 1px 4px 0 rgba(122, 122, 122, 0.6);
}

.shadow-2dp {
  box-shadow: 0 2px 12px 0 rgba(122, 122, 122, 0.6);
}

.shadow-3dp {
  box-shadow: 0 3px 20px 0 rgba(122, 122, 122, 0.6);
}

.shadow-4dp {
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}

.shadow-6dp {
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}

.shadow-8dp {
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}

.shadow-16dp {
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}

.shadow-24dp {
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}

.shadow-button {
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}

.shadow-button-hover {
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}

.shadow-1dp-primary {
  box-shadow: 0 1px 4px 0 rgba(102, 51, 153, 0.6);
}

.shadow-2dp-primary {
  box-shadow: 0 2px 12px 0 rgba(102, 51, 153, 0.6);
}

.shadow-3dp-primary {
  box-shadow: 0 3px 20px 0 rgba(102, 51, 153, 0.6);
}

.shadow-4dp-primary {
  box-shadow: 0 4px 20px 0 rgba(102, 51, 153, 0.6);
}

.shadow-6dp-primary {
  box-shadow: 0 4px 20px 0 rgba(102, 51, 153, 0.6);
}

.shadow-8dp-primary {
  box-shadow: 0 4px 20px 0 rgba(102, 51, 153, 0.6);
}

.shadow-16dp-primary {
  box-shadow: 0 4px 20px 0 rgba(102, 51, 153, 0.6);
}

.shadow-24dp-primary {
  box-shadow: 0 4px 20px 0 rgba(102, 51, 153, 0.6);
}

.shadow-1dp-secondary {
  box-shadow: 0 1px 4px 0 rgba(31, 31, 31, 0.6);
}

.shadow-2dp-secondary {
  box-shadow: 0 2px 12px 0 rgba(31, 31, 31, 0.6);
}

.shadow-3dp-secondary {
  box-shadow: 0 3px 20px 0 rgba(31, 31, 31, 0.6);
}

.shadow-4dp-secondary {
  box-shadow: 0 4px 20px 0 rgba(31, 31, 31, 0.6);
}

.shadow-6dp-secondary {
  box-shadow: 0 4px 20px 0 rgba(31, 31, 31, 0.6);
}

.shadow-8dp-secondary {
  box-shadow: 0 4px 20px 0 rgba(31, 31, 31, 0.6);
}

.shadow-16dp-secondary {
  box-shadow: 0 4px 20px 0 rgba(31, 31, 31, 0.6);
}

.shadow-24dp-secondary {
  box-shadow: 0 4px 20px 0 rgba(31, 31, 31, 0.6);
}

.shadow-1dp-success {
  box-shadow: 0 1px 4px 0 rgba(76, 175, 80, 0.6);
}

.shadow-2dp-success {
  box-shadow: 0 2px 12px 0 rgba(76, 175, 80, 0.6);
}

.shadow-3dp-success {
  box-shadow: 0 3px 20px 0 rgba(76, 175, 80, 0.6);
}

.shadow-4dp-success {
  box-shadow: 0 4px 20px 0 rgba(76, 175, 80, 0.6);
}

.shadow-6dp-success {
  box-shadow: 0 4px 20px 0 rgba(76, 175, 80, 0.6);
}

.shadow-8dp-success {
  box-shadow: 0 4px 20px 0 rgba(76, 175, 80, 0.6);
}

.shadow-16dp-success {
  box-shadow: 0 4px 20px 0 rgba(76, 175, 80, 0.6);
}

.shadow-24dp-success {
  box-shadow: 0 4px 20px 0 rgba(76, 175, 80, 0.6);
}

.shadow-1dp-info {
  box-shadow: 0 1px 4px 0 rgba(0, 52, 115, 0.6);
}

.shadow-2dp-info {
  box-shadow: 0 2px 12px 0 rgba(0, 52, 115, 0.6);
}

.shadow-3dp-info {
  box-shadow: 0 3px 20px 0 rgba(0, 52, 115, 0.6);
}

.shadow-4dp-info {
  box-shadow: 0 4px 20px 0 rgba(0, 52, 115, 0.6);
}

.shadow-6dp-info {
  box-shadow: 0 4px 20px 0 rgba(0, 52, 115, 0.6);
}

.shadow-8dp-info {
  box-shadow: 0 4px 20px 0 rgba(0, 52, 115, 0.6);
}

.shadow-16dp-info {
  box-shadow: 0 4px 20px 0 rgba(0, 52, 115, 0.6);
}

.shadow-24dp-info {
  box-shadow: 0 4px 20px 0 rgba(0, 52, 115, 0.6);
}

.shadow-1dp-warning {
  box-shadow: 0 1px 4px 0 rgba(233, 125, 35, 0.6);
}

.shadow-2dp-warning {
  box-shadow: 0 2px 12px 0 rgba(233, 125, 35, 0.6);
}

.shadow-3dp-warning {
  box-shadow: 0 3px 20px 0 rgba(233, 125, 35, 0.6);
}

.shadow-4dp-warning {
  box-shadow: 0 4px 20px 0 rgba(233, 125, 35, 0.6);
}

.shadow-6dp-warning {
  box-shadow: 0 4px 20px 0 rgba(233, 125, 35, 0.6);
}

.shadow-8dp-warning {
  box-shadow: 0 4px 20px 0 rgba(233, 125, 35, 0.6);
}

.shadow-16dp-warning {
  box-shadow: 0 4px 20px 0 rgba(233, 125, 35, 0.6);
}

.shadow-24dp-warning {
  box-shadow: 0 4px 20px 0 rgba(233, 125, 35, 0.6);
}

.shadow-1dp-danger {
  box-shadow: 0 1px 4px 0 rgba(244, 67, 54, 0.6);
}

.shadow-2dp-danger {
  box-shadow: 0 2px 12px 0 rgba(244, 67, 54, 0.6);
}

.shadow-3dp-danger {
  box-shadow: 0 3px 20px 0 rgba(244, 67, 54, 0.6);
}

.shadow-4dp-danger {
  box-shadow: 0 4px 20px 0 rgba(244, 67, 54, 0.6);
}

.shadow-6dp-danger {
  box-shadow: 0 4px 20px 0 rgba(244, 67, 54, 0.6);
}

.shadow-8dp-danger {
  box-shadow: 0 4px 20px 0 rgba(244, 67, 54, 0.6);
}

.shadow-16dp-danger {
  box-shadow: 0 4px 20px 0 rgba(244, 67, 54, 0.6);
}

.shadow-24dp-danger {
  box-shadow: 0 4px 20px 0 rgba(244, 67, 54, 0.6);
}

.shadow-1dp-light {
  box-shadow: 0 1px 4px 0 rgba(102, 102, 102, 0.6);
}

.shadow-2dp-light {
  box-shadow: 0 2px 12px 0 rgba(102, 102, 102, 0.6);
}

.shadow-3dp-light {
  box-shadow: 0 3px 20px 0 rgba(102, 102, 102, 0.6);
}

.shadow-4dp-light {
  box-shadow: 0 4px 20px 0 rgba(102, 102, 102, 0.6);
}

.shadow-6dp-light {
  box-shadow: 0 4px 20px 0 rgba(102, 102, 102, 0.6);
}

.shadow-8dp-light {
  box-shadow: 0 4px 20px 0 rgba(102, 102, 102, 0.6);
}

.shadow-16dp-light {
  box-shadow: 0 4px 20px 0 rgba(102, 102, 102, 0.6);
}

.shadow-24dp-light {
  box-shadow: 0 4px 20px 0 rgba(102, 102, 102, 0.6);
}

.shadow-1dp-dark {
  box-shadow: 0 1px 4px 0 rgba(10, 10, 10, 0.6);
}

.shadow-2dp-dark {
  box-shadow: 0 2px 12px 0 rgba(10, 10, 10, 0.6);
}

.shadow-3dp-dark {
  box-shadow: 0 3px 20px 0 rgba(10, 10, 10, 0.6);
}

.shadow-4dp-dark {
  box-shadow: 0 4px 20px 0 rgba(10, 10, 10, 0.6);
}

.shadow-6dp-dark {
  box-shadow: 0 4px 20px 0 rgba(10, 10, 10, 0.6);
}

.shadow-8dp-dark {
  box-shadow: 0 4px 20px 0 rgba(10, 10, 10, 0.6);
}

.shadow-16dp-dark {
  box-shadow: 0 4px 20px 0 rgba(10, 10, 10, 0.6);
}

.shadow-24dp-dark {
  box-shadow: 0 4px 20px 0 rgba(10, 10, 10, 0.6);
}

.alert {
  position: relative;
  padding: 1rem 1.5rem;
  color: #fff !important;
  border: 1px solid transparent !important;
}
.alert .alert-link {
  color: #999999;
}
.alert .close {
  position: absolute;
  right: 0.8rem;
}
.alert.alert-primary {
  background-color: #8b5cf6;
}
.alert.alert-raised-primary {
  background-color: #8b5cf6;
  box-shadow: 0 2px 12px 0 rgba(102, 51, 153, 0.6);
}
.alert.alert-secondary {
  background-color: #1f1f1f;
}
.alert.alert-raised-secondary {
  background-color: #1f1f1f;
  box-shadow: 0 2px 12px 0 rgba(31, 31, 31, 0.6);
}
.alert.alert-success {
  background-color: #4caf50;
}
.alert.alert-raised-success {
  background-color: #4caf50;
  box-shadow: 0 2px 12px 0 rgba(76, 175, 80, 0.6);
}
.alert.alert-info {
  background-color: #003473;
}
.alert.alert-raised-info {
  background-color: #003473;
  box-shadow: 0 2px 12px 0 rgba(0, 52, 115, 0.6);
}
.alert.alert-warning {
  background-color: #e97d23;
}
.alert.alert-raised-warning {
  background-color: #e97d23;
  box-shadow: 0 2px 12px 0 rgba(233, 125, 35, 0.6);
}
.alert.alert-danger {
  background-color: #f44336;
}
.alert.alert-raised-danger {
  background-color: #f44336;
  box-shadow: 0 2px 12px 0 rgba(244, 67, 54, 0.6);
}
.alert.alert-light {
  background-color: #666666;
}
.alert.alert-raised-light {
  background-color: #666666;
  box-shadow: 0 2px 12px 0 rgba(102, 102, 102, 0.6);
}
.alert.alert-dark {
  background-color: #0a0a0a;
}
.alert.alert-raised-dark {
  background-color: #0a0a0a;
  box-shadow: 0 2px 12px 0 rgba(10, 10, 10, 0.6);
}

.card {
  border: 0;
  box-shadow: 0 2px 12px 0 rgba(122, 122, 122, 0.6);
}
.card .card-header {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: transparent;
}
.card .card-footer {
  background: transparent;
}
.card .card-header,
.card .card-footer {
  border-color: #999999;
}
.card .card-title {
  margin: 0 0 1rem;
  font-weight: 500;
}

.image-bg {
  overflow: hidden;
}
.image-bg .image-bg-content {
  display: flex;
  flex-direction: column;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
}
.image-bg .image-bg-content .image-bg-footer h4 {
  margin: 0 0 1rem;
}

.accordion .card {
  margin-bottom: 1rem;
  border-radius: 0.25rem !important;
}

.accordion .card-header {
  cursor: pointer;
}
.accordion .card-header .i-arrow {
  transform: rotate(0deg);
  transition: all 0.2s ease-in-out !important;
}
.accordion .card-header[aria-expanded="true"] .i-arrow {
  transform: rotate(-90deg);
}

.breadcrumb {
  background-color: #f9f9f9;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "/";
}

html [type="button"],
.btn[type="button"] {
  -webkit-appearance: none !important;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 0.875rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  padding: 0.53125rem 1.5rem;
  transition: all 0.2s ease-in-out !important;
}
.btn.btn-primary {
  color: #fff;
  background-color: #8b5cf6;
  border-color: #8b5cf6;
  box-shadow: none;
}
.btn.btn-primary:hover,
.btn.btn-primary:active {
  color: #fff !important;
  background-color: #7339ac !important;
  border-color: #7339ac !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-primary:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #7339ac !important;
  border-color: #7339ac !important;
}
.btn.btn-primary.active,
.open > .btn.btn-primary.dropdown-toggle,
.show > .btn.btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #7339ac !important;
  border-color: #7339ac !important;
}
.btn.btn-primary.active:hover,
.btn.btn-primary.active:focus,
.btn.btn-primary.active.focus,
.open > .btn.btn-primary.dropdown-toggle:hover,
.open > .btn.btn-primary.dropdown-toggle:focus,
.open > .btn.btn-primary.dropdown-toggle.focus,
.show > .btn.btn-primary.dropdown-toggle:hover,
.show > .btn.btn-primary.dropdown-toggle:focus,
.show > .btn.btn-primary.dropdown-toggle.focus {
  color: #fff;
  background-color: #7339ac;
  border-color: #7339ac;
}
.open > .btn.btn-primary.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #8b5cf6;
}
.open > .btn.btn-primary.dropdown-toggle.btn-icon:hover {
  background-color: #7339ac;
}
.btn.btn-primary.disabled:focus,
.btn.btn-primary.disabled.focus,
.btn.btn-primary:disabled:focus,
.btn.btn-primary:disabled.focus {
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
}
.btn.btn-primary.disabled:hover,
.btn.btn-primary:disabled:hover {
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-primary {
  color: #8b5cf6;
  background-color: #fff;
  border-color: #8b5cf6;
  box-shadow: none;
}
.btn.btn-outline-primary:hover,
.btn.btn-outline-primary:active {
  color: #fff !important;
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-primary:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
}
.btn.btn-outline-primary.active,
.open > .btn.btn-outline-primary.dropdown-toggle,
.show > .btn.btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
}
.btn.btn-outline-primary.active:hover,
.btn.btn-outline-primary.active:focus,
.btn.btn-outline-primary.active.focus,
.open > .btn.btn-outline-primary.dropdown-toggle:hover,
.open > .btn.btn-outline-primary.dropdown-toggle:focus,
.open > .btn.btn-outline-primary.dropdown-toggle.focus,
.show > .btn.btn-outline-primary.dropdown-toggle:hover,
.show > .btn.btn-outline-primary.dropdown-toggle:focus,
.show > .btn.btn-outline-primary.dropdown-toggle.focus {
  color: #fff;
  background-color: #8b5cf6;
  border-color: #8b5cf6;
}
.open > .btn.btn-outline-primary.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-primary.dropdown-toggle.btn-icon:hover {
  background-color: #8b5cf6;
}
.btn.btn-outline-primary.disabled:focus,
.btn.btn-outline-primary.disabled.focus,
.btn.btn-outline-primary:disabled:focus,
.btn.btn-outline-primary:disabled.focus {
  background-color: #fff !important;
  border-color: #8b5cf6 !important;
}
.btn.btn-outline-primary.disabled:hover,
.btn.btn-outline-primary:disabled:hover {
  background-color: #fff !important;
  border-color: #8b5cf6 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-primary {
  color: #fff;
  background-color: #8b5cf6;
  border-color: #8b5cf6;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-primary:hover,
.btn.btn-raised-primary:active {
  color: #fff !important;
  background-color: #7339ac !important;
  border-color: #7339ac !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-primary:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #7339ac !important;
  border-color: #7339ac !important;
}
.btn.btn-raised-primary.active,
.open > .btn.btn-raised-primary.dropdown-toggle,
.show > .btn.btn-raised-primary.dropdown-toggle {
  color: #fff;
  background-color: #7339ac !important;
  border-color: #7339ac !important;
}
.btn.btn-raised-primary.active:hover,
.btn.btn-raised-primary.active:focus,
.btn.btn-raised-primary.active.focus,
.open > .btn.btn-raised-primary.dropdown-toggle:hover,
.open > .btn.btn-raised-primary.dropdown-toggle:focus,
.open > .btn.btn-raised-primary.dropdown-toggle.focus,
.show > .btn.btn-raised-primary.dropdown-toggle:hover,
.show > .btn.btn-raised-primary.dropdown-toggle:focus,
.show > .btn.btn-raised-primary.dropdown-toggle.focus {
  color: #fff;
  background-color: #7339ac;
  border-color: #7339ac;
}
.open > .btn.btn-raised-primary.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #8b5cf6;
}
.open > .btn.btn-raised-primary.dropdown-toggle.btn-icon:hover {
  background-color: #7339ac;
}
.btn.btn-raised-primary.disabled:focus,
.btn.btn-raised-primary.disabled.focus,
.btn.btn-raised-primary:disabled:focus,
.btn.btn-raised-primary:disabled.focus {
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
}
.btn.btn-raised-primary.disabled:hover,
.btn.btn-raised-primary:disabled:hover {
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-primary {
  color: #8b5cf6 !important;
}
.btn.btn-link-primary::after {
  background-color: #8b5cf6 !important;
}
.btn.btn-secondary {
  color: #fff;
  background-color: #1f1f1f;
  border-color: #1f1f1f;
  box-shadow: none;
}
.btn.btn-secondary:hover,
.btn.btn-secondary:active {
  color: #fff !important;
  background-color: #2b2b2b !important;
  border-color: #2b2b2b !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-secondary:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #2b2b2b !important;
  border-color: #2b2b2b !important;
}
.btn.btn-secondary.active,
.open > .btn.btn-secondary.dropdown-toggle,
.show > .btn.btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #2b2b2b !important;
  border-color: #2b2b2b !important;
}
.btn.btn-secondary.active:hover,
.btn.btn-secondary.active:focus,
.btn.btn-secondary.active.focus,
.open > .btn.btn-secondary.dropdown-toggle:hover,
.open > .btn.btn-secondary.dropdown-toggle:focus,
.open > .btn.btn-secondary.dropdown-toggle.focus,
.show > .btn.btn-secondary.dropdown-toggle:hover,
.show > .btn.btn-secondary.dropdown-toggle:focus,
.show > .btn.btn-secondary.dropdown-toggle.focus {
  color: #fff;
  background-color: #2b2b2b;
  border-color: #2b2b2b;
}
.open > .btn.btn-secondary.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #1f1f1f;
}
.open > .btn.btn-secondary.dropdown-toggle.btn-icon:hover {
  background-color: #2b2b2b;
}
.btn.btn-secondary.disabled:focus,
.btn.btn-secondary.disabled.focus,
.btn.btn-secondary:disabled:focus,
.btn.btn-secondary:disabled.focus {
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
}
.btn.btn-secondary.disabled:hover,
.btn.btn-secondary:disabled:hover {
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-secondary {
  color: #1f1f1f;
  background-color: #fff;
  border-color: #1f1f1f;
  box-shadow: none;
}
.btn.btn-outline-secondary:hover,
.btn.btn-outline-secondary:active {
  color: #fff !important;
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-secondary:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
}
.btn.btn-outline-secondary.active,
.open > .btn.btn-outline-secondary.dropdown-toggle,
.show > .btn.btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
}
.btn.btn-outline-secondary.active:hover,
.btn.btn-outline-secondary.active:focus,
.btn.btn-outline-secondary.active.focus,
.open > .btn.btn-outline-secondary.dropdown-toggle:hover,
.open > .btn.btn-outline-secondary.dropdown-toggle:focus,
.open > .btn.btn-outline-secondary.dropdown-toggle.focus,
.show > .btn.btn-outline-secondary.dropdown-toggle:hover,
.show > .btn.btn-outline-secondary.dropdown-toggle:focus,
.show > .btn.btn-outline-secondary.dropdown-toggle.focus {
  color: #fff;
  background-color: #1f1f1f;
  border-color: #1f1f1f;
}
.open > .btn.btn-outline-secondary.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-secondary.dropdown-toggle.btn-icon:hover {
  background-color: #1f1f1f;
}
.btn.btn-outline-secondary.disabled:focus,
.btn.btn-outline-secondary.disabled.focus,
.btn.btn-outline-secondary:disabled:focus,
.btn.btn-outline-secondary:disabled.focus {
  background-color: #fff !important;
  border-color: #1f1f1f !important;
}
.btn.btn-outline-secondary.disabled:hover,
.btn.btn-outline-secondary:disabled:hover {
  background-color: #fff !important;
  border-color: #1f1f1f !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-secondary {
  color: #fff;
  background-color: #1f1f1f;
  border-color: #1f1f1f;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-secondary:hover,
.btn.btn-raised-secondary:active {
  color: #fff !important;
  background-color: #2b2b2b !important;
  border-color: #2b2b2b !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-secondary:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #2b2b2b !important;
  border-color: #2b2b2b !important;
}
.btn.btn-raised-secondary.active,
.open > .btn.btn-raised-secondary.dropdown-toggle,
.show > .btn.btn-raised-secondary.dropdown-toggle {
  color: #fff;
  background-color: #2b2b2b !important;
  border-color: #2b2b2b !important;
}
.btn.btn-raised-secondary.active:hover,
.btn.btn-raised-secondary.active:focus,
.btn.btn-raised-secondary.active.focus,
.open > .btn.btn-raised-secondary.dropdown-toggle:hover,
.open > .btn.btn-raised-secondary.dropdown-toggle:focus,
.open > .btn.btn-raised-secondary.dropdown-toggle.focus,
.show > .btn.btn-raised-secondary.dropdown-toggle:hover,
.show > .btn.btn-raised-secondary.dropdown-toggle:focus,
.show > .btn.btn-raised-secondary.dropdown-toggle.focus {
  color: #fff;
  background-color: #2b2b2b;
  border-color: #2b2b2b;
}
.open > .btn.btn-raised-secondary.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #1f1f1f;
}
.open > .btn.btn-raised-secondary.dropdown-toggle.btn-icon:hover {
  background-color: #2b2b2b;
}
.btn.btn-raised-secondary.disabled:focus,
.btn.btn-raised-secondary.disabled.focus,
.btn.btn-raised-secondary:disabled:focus,
.btn.btn-raised-secondary:disabled.focus {
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
}
.btn.btn-raised-secondary.disabled:hover,
.btn.btn-raised-secondary:disabled:hover {
  background-color: #1f1f1f !important;
  border-color: #1f1f1f !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-secondary {
  color: #1f1f1f !important;
}
.btn.btn-link-secondary::after {
  background-color: #1f1f1f !important;
}
.btn.btn-success {
  color: #fff;
  background-color: #4caf50;
  border-color: #4caf50;
  box-shadow: none;
}
.btn.btn-success:hover,
.btn.btn-success:active {
  color: #fff !important;
  background-color: #5cb860 !important;
  border-color: #5cb860 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-success:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #5cb860 !important;
  border-color: #5cb860 !important;
}
.btn.btn-success.active,
.open > .btn.btn-success.dropdown-toggle,
.show > .btn.btn-success.dropdown-toggle {
  color: #fff;
  background-color: #5cb860 !important;
  border-color: #5cb860 !important;
}
.btn.btn-success.active:hover,
.btn.btn-success.active:focus,
.btn.btn-success.active.focus,
.open > .btn.btn-success.dropdown-toggle:hover,
.open > .btn.btn-success.dropdown-toggle:focus,
.open > .btn.btn-success.dropdown-toggle.focus,
.show > .btn.btn-success.dropdown-toggle:hover,
.show > .btn.btn-success.dropdown-toggle:focus,
.show > .btn.btn-success.dropdown-toggle.focus {
  color: #fff;
  background-color: #5cb860;
  border-color: #5cb860;
}
.open > .btn.btn-success.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #4caf50;
}
.open > .btn.btn-success.dropdown-toggle.btn-icon:hover {
  background-color: #5cb860;
}
.btn.btn-success.disabled:focus,
.btn.btn-success.disabled.focus,
.btn.btn-success:disabled:focus,
.btn.btn-success:disabled.focus {
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
}
.btn.btn-success.disabled:hover,
.btn.btn-success:disabled:hover {
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-success {
  color: #4caf50;
  background-color: #fff;
  border-color: #4caf50;
  box-shadow: none;
}
.btn.btn-outline-success:hover,
.btn.btn-outline-success:active {
  color: #fff !important;
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-success:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
}
.btn.btn-outline-success.active,
.open > .btn.btn-outline-success.dropdown-toggle,
.show > .btn.btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
}
.btn.btn-outline-success.active:hover,
.btn.btn-outline-success.active:focus,
.btn.btn-outline-success.active.focus,
.open > .btn.btn-outline-success.dropdown-toggle:hover,
.open > .btn.btn-outline-success.dropdown-toggle:focus,
.open > .btn.btn-outline-success.dropdown-toggle.focus,
.show > .btn.btn-outline-success.dropdown-toggle:hover,
.show > .btn.btn-outline-success.dropdown-toggle:focus,
.show > .btn.btn-outline-success.dropdown-toggle.focus {
  color: #fff;
  background-color: #4caf50;
  border-color: #4caf50;
}
.open > .btn.btn-outline-success.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-success.dropdown-toggle.btn-icon:hover {
  background-color: #4caf50;
}
.btn.btn-outline-success.disabled:focus,
.btn.btn-outline-success.disabled.focus,
.btn.btn-outline-success:disabled:focus,
.btn.btn-outline-success:disabled.focus {
  background-color: #fff !important;
  border-color: #4caf50 !important;
}
.btn.btn-outline-success.disabled:hover,
.btn.btn-outline-success:disabled:hover {
  background-color: #fff !important;
  border-color: #4caf50 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-success {
  color: #fff;
  background-color: #4caf50;
  border-color: #4caf50;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-success:hover,
.btn.btn-raised-success:active {
  color: #fff !important;
  background-color: #5cb860 !important;
  border-color: #5cb860 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-success:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #5cb860 !important;
  border-color: #5cb860 !important;
}
.btn.btn-raised-success.active,
.open > .btn.btn-raised-success.dropdown-toggle,
.show > .btn.btn-raised-success.dropdown-toggle {
  color: #fff;
  background-color: #5cb860 !important;
  border-color: #5cb860 !important;
}
.btn.btn-raised-success.active:hover,
.btn.btn-raised-success.active:focus,
.btn.btn-raised-success.active.focus,
.open > .btn.btn-raised-success.dropdown-toggle:hover,
.open > .btn.btn-raised-success.dropdown-toggle:focus,
.open > .btn.btn-raised-success.dropdown-toggle.focus,
.show > .btn.btn-raised-success.dropdown-toggle:hover,
.show > .btn.btn-raised-success.dropdown-toggle:focus,
.show > .btn.btn-raised-success.dropdown-toggle.focus {
  color: #fff;
  background-color: #5cb860;
  border-color: #5cb860;
}
.open > .btn.btn-raised-success.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #4caf50;
}
.open > .btn.btn-raised-success.dropdown-toggle.btn-icon:hover {
  background-color: #5cb860;
}
.btn.btn-raised-success.disabled:focus,
.btn.btn-raised-success.disabled.focus,
.btn.btn-raised-success:disabled:focus,
.btn.btn-raised-success:disabled.focus {
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
}
.btn.btn-raised-success.disabled:hover,
.btn.btn-raised-success:disabled:hover {
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-success {
  color: #4caf50 !important;
}
.btn.btn-link-success::after {
  background-color: #4caf50 !important;
}
.btn.btn-info {
  color: #fff;
  background-color: #003473;
  border-color: #003473;
  box-shadow: none;
}
.btn.btn-info:hover,
.btn.btn-info:active {
  color: #fff !important;
  background-color: #00408d !important;
  border-color: #00408d !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-info:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #00408d !important;
  border-color: #00408d !important;
}
.btn.btn-info.active,
.open > .btn.btn-info.dropdown-toggle,
.show > .btn.btn-info.dropdown-toggle {
  color: #fff;
  background-color: #00408d !important;
  border-color: #00408d !important;
}
.btn.btn-info.active:hover,
.btn.btn-info.active:focus,
.btn.btn-info.active.focus,
.open > .btn.btn-info.dropdown-toggle:hover,
.open > .btn.btn-info.dropdown-toggle:focus,
.open > .btn.btn-info.dropdown-toggle.focus,
.show > .btn.btn-info.dropdown-toggle:hover,
.show > .btn.btn-info.dropdown-toggle:focus,
.show > .btn.btn-info.dropdown-toggle.focus {
  color: #fff;
  background-color: #00408d;
  border-color: #00408d;
}
.open > .btn.btn-info.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #003473;
}
.open > .btn.btn-info.dropdown-toggle.btn-icon:hover {
  background-color: #00408d;
}
.btn.btn-info.disabled:focus,
.btn.btn-info.disabled.focus,
.btn.btn-info:disabled:focus,
.btn.btn-info:disabled.focus {
  background-color: #003473 !important;
  border-color: #003473 !important;
}
.btn.btn-info.disabled:hover,
.btn.btn-info:disabled:hover {
  background-color: #003473 !important;
  border-color: #003473 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-info {
  color: #003473;
  background-color: #fff;
  border-color: #003473;
  box-shadow: none;
}
.btn.btn-outline-info:hover,
.btn.btn-outline-info:active {
  color: #fff !important;
  background-color: #003473 !important;
  border-color: #003473 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-info:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #003473 !important;
  border-color: #003473 !important;
}
.btn.btn-outline-info.active,
.open > .btn.btn-outline-info.dropdown-toggle,
.show > .btn.btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #003473 !important;
  border-color: #003473 !important;
}
.btn.btn-outline-info.active:hover,
.btn.btn-outline-info.active:focus,
.btn.btn-outline-info.active.focus,
.open > .btn.btn-outline-info.dropdown-toggle:hover,
.open > .btn.btn-outline-info.dropdown-toggle:focus,
.open > .btn.btn-outline-info.dropdown-toggle.focus,
.show > .btn.btn-outline-info.dropdown-toggle:hover,
.show > .btn.btn-outline-info.dropdown-toggle:focus,
.show > .btn.btn-outline-info.dropdown-toggle.focus {
  color: #fff;
  background-color: #003473;
  border-color: #003473;
}
.open > .btn.btn-outline-info.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-info.dropdown-toggle.btn-icon:hover {
  background-color: #003473;
}
.btn.btn-outline-info.disabled:focus,
.btn.btn-outline-info.disabled.focus,
.btn.btn-outline-info:disabled:focus,
.btn.btn-outline-info:disabled.focus {
  background-color: #fff !important;
  border-color: #003473 !important;
}
.btn.btn-outline-info.disabled:hover,
.btn.btn-outline-info:disabled:hover {
  background-color: #fff !important;
  border-color: #003473 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-info {
  color: #fff;
  background-color: #003473;
  border-color: #003473;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-info:hover,
.btn.btn-raised-info:active {
  color: #fff !important;
  background-color: #00408d !important;
  border-color: #00408d !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-info:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #00408d !important;
  border-color: #00408d !important;
}
.btn.btn-raised-info.active,
.open > .btn.btn-raised-info.dropdown-toggle,
.show > .btn.btn-raised-info.dropdown-toggle {
  color: #fff;
  background-color: #00408d !important;
  border-color: #00408d !important;
}
.btn.btn-raised-info.active:hover,
.btn.btn-raised-info.active:focus,
.btn.btn-raised-info.active.focus,
.open > .btn.btn-raised-info.dropdown-toggle:hover,
.open > .btn.btn-raised-info.dropdown-toggle:focus,
.open > .btn.btn-raised-info.dropdown-toggle.focus,
.show > .btn.btn-raised-info.dropdown-toggle:hover,
.show > .btn.btn-raised-info.dropdown-toggle:focus,
.show > .btn.btn-raised-info.dropdown-toggle.focus {
  color: #fff;
  background-color: #00408d;
  border-color: #00408d;
}
.open > .btn.btn-raised-info.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #003473;
}
.open > .btn.btn-raised-info.dropdown-toggle.btn-icon:hover {
  background-color: #00408d;
}
.btn.btn-raised-info.disabled:focus,
.btn.btn-raised-info.disabled.focus,
.btn.btn-raised-info:disabled:focus,
.btn.btn-raised-info:disabled.focus {
  background-color: #003473 !important;
  border-color: #003473 !important;
}
.btn.btn-raised-info.disabled:hover,
.btn.btn-raised-info:disabled:hover {
  background-color: #003473 !important;
  border-color: #003473 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-info {
  color: #003473 !important;
}
.btn.btn-link-info::after {
  background-color: #003473 !important;
}
.btn.btn-warning {
  color: #fff;
  background-color: #e97d23;
  border-color: #e97d23;
  box-shadow: none;
}
.btn.btn-warning:hover,
.btn.btn-warning:active {
  color: #fff !important;
  background-color: #eb8b3a !important;
  border-color: #eb8b3a !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-warning:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #eb8b3a !important;
  border-color: #eb8b3a !important;
}
.btn.btn-warning.active,
.open > .btn.btn-warning.dropdown-toggle,
.show > .btn.btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #eb8b3a !important;
  border-color: #eb8b3a !important;
}
.btn.btn-warning.active:hover,
.btn.btn-warning.active:focus,
.btn.btn-warning.active.focus,
.open > .btn.btn-warning.dropdown-toggle:hover,
.open > .btn.btn-warning.dropdown-toggle:focus,
.open > .btn.btn-warning.dropdown-toggle.focus,
.show > .btn.btn-warning.dropdown-toggle:hover,
.show > .btn.btn-warning.dropdown-toggle:focus,
.show > .btn.btn-warning.dropdown-toggle.focus {
  color: #fff;
  background-color: #eb8b3a;
  border-color: #eb8b3a;
}
.open > .btn.btn-warning.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #e97d23;
}
.open > .btn.btn-warning.dropdown-toggle.btn-icon:hover {
  background-color: #eb8b3a;
}
.btn.btn-warning.disabled:focus,
.btn.btn-warning.disabled.focus,
.btn.btn-warning:disabled:focus,
.btn.btn-warning:disabled.focus {
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
}
.btn.btn-warning.disabled:hover,
.btn.btn-warning:disabled:hover {
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-warning {
  color: #e97d23;
  background-color: #fff;
  border-color: #e97d23;
  box-shadow: none;
}
.btn.btn-outline-warning:hover,
.btn.btn-outline-warning:active {
  color: #fff !important;
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-warning:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
}
.btn.btn-outline-warning.active,
.open > .btn.btn-outline-warning.dropdown-toggle,
.show > .btn.btn-outline-warning.dropdown-toggle {
  color: #fff;
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
}
.btn.btn-outline-warning.active:hover,
.btn.btn-outline-warning.active:focus,
.btn.btn-outline-warning.active.focus,
.open > .btn.btn-outline-warning.dropdown-toggle:hover,
.open > .btn.btn-outline-warning.dropdown-toggle:focus,
.open > .btn.btn-outline-warning.dropdown-toggle.focus,
.show > .btn.btn-outline-warning.dropdown-toggle:hover,
.show > .btn.btn-outline-warning.dropdown-toggle:focus,
.show > .btn.btn-outline-warning.dropdown-toggle.focus {
  color: #fff;
  background-color: #e97d23;
  border-color: #e97d23;
}
.open > .btn.btn-outline-warning.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-warning.dropdown-toggle.btn-icon:hover {
  background-color: #e97d23;
}
.btn.btn-outline-warning.disabled:focus,
.btn.btn-outline-warning.disabled.focus,
.btn.btn-outline-warning:disabled:focus,
.btn.btn-outline-warning:disabled.focus {
  background-color: #fff !important;
  border-color: #e97d23 !important;
}
.btn.btn-outline-warning.disabled:hover,
.btn.btn-outline-warning:disabled:hover {
  background-color: #fff !important;
  border-color: #e97d23 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-warning {
  color: #fff;
  background-color: #e97d23;
  border-color: #e97d23;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-warning:hover,
.btn.btn-raised-warning:active {
  color: #fff !important;
  background-color: #eb8b3a !important;
  border-color: #eb8b3a !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-warning:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #eb8b3a !important;
  border-color: #eb8b3a !important;
}
.btn.btn-raised-warning.active,
.open > .btn.btn-raised-warning.dropdown-toggle,
.show > .btn.btn-raised-warning.dropdown-toggle {
  color: #fff;
  background-color: #eb8b3a !important;
  border-color: #eb8b3a !important;
}
.btn.btn-raised-warning.active:hover,
.btn.btn-raised-warning.active:focus,
.btn.btn-raised-warning.active.focus,
.open > .btn.btn-raised-warning.dropdown-toggle:hover,
.open > .btn.btn-raised-warning.dropdown-toggle:focus,
.open > .btn.btn-raised-warning.dropdown-toggle.focus,
.show > .btn.btn-raised-warning.dropdown-toggle:hover,
.show > .btn.btn-raised-warning.dropdown-toggle:focus,
.show > .btn.btn-raised-warning.dropdown-toggle.focus {
  color: #fff;
  background-color: #eb8b3a;
  border-color: #eb8b3a;
}
.open > .btn.btn-raised-warning.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #e97d23;
}
.open > .btn.btn-raised-warning.dropdown-toggle.btn-icon:hover {
  background-color: #eb8b3a;
}
.btn.btn-raised-warning.disabled:focus,
.btn.btn-raised-warning.disabled.focus,
.btn.btn-raised-warning:disabled:focus,
.btn.btn-raised-warning:disabled.focus {
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
}
.btn.btn-raised-warning.disabled:hover,
.btn.btn-raised-warning:disabled:hover {
  background-color: #e97d23 !important;
  border-color: #e97d23 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-warning {
  color: #e97d23 !important;
}
.btn.btn-link-warning::after {
  background-color: #e97d23 !important;
}
.btn.btn-danger {
  color: #fff;
  background-color: #f44336;
  border-color: #f44336;
  box-shadow: none;
}
.btn.btn-danger:hover,
.btn.btn-danger:active {
  color: #fff !important;
  background-color: #f55a4e !important;
  border-color: #f55a4e !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-danger:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #f55a4e !important;
  border-color: #f55a4e !important;
}
.btn.btn-danger.active,
.open > .btn.btn-danger.dropdown-toggle,
.show > .btn.btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #f55a4e !important;
  border-color: #f55a4e !important;
}
.btn.btn-danger.active:hover,
.btn.btn-danger.active:focus,
.btn.btn-danger.active.focus,
.open > .btn.btn-danger.dropdown-toggle:hover,
.open > .btn.btn-danger.dropdown-toggle:focus,
.open > .btn.btn-danger.dropdown-toggle.focus,
.show > .btn.btn-danger.dropdown-toggle:hover,
.show > .btn.btn-danger.dropdown-toggle:focus,
.show > .btn.btn-danger.dropdown-toggle.focus {
  color: #fff;
  background-color: #f55a4e;
  border-color: #f55a4e;
}
.open > .btn.btn-danger.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #f44336;
}
.open > .btn.btn-danger.dropdown-toggle.btn-icon:hover {
  background-color: #f55a4e;
}
.btn.btn-danger.disabled:focus,
.btn.btn-danger.disabled.focus,
.btn.btn-danger:disabled:focus,
.btn.btn-danger:disabled.focus {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
}
.btn.btn-danger.disabled:hover,
.btn.btn-danger:disabled:hover {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-danger {
  color: #f44336;
  background-color: #fff;
  border-color: #f44336;
  box-shadow: none;
}
.btn.btn-outline-danger:hover,
.btn.btn-outline-danger:active {
  color: #fff !important;
  background-color: #f44336 !important;
  border-color: #f44336 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-danger:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #f44336 !important;
  border-color: #f44336 !important;
}
.btn.btn-outline-danger.active,
.open > .btn.btn-outline-danger.dropdown-toggle,
.show > .btn.btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #f44336 !important;
  border-color: #f44336 !important;
}
.btn.btn-outline-danger.active:hover,
.btn.btn-outline-danger.active:focus,
.btn.btn-outline-danger.active.focus,
.open > .btn.btn-outline-danger.dropdown-toggle:hover,
.open > .btn.btn-outline-danger.dropdown-toggle:focus,
.open > .btn.btn-outline-danger.dropdown-toggle.focus,
.show > .btn.btn-outline-danger.dropdown-toggle:hover,
.show > .btn.btn-outline-danger.dropdown-toggle:focus,
.show > .btn.btn-outline-danger.dropdown-toggle.focus {
  color: #fff;
  background-color: #f44336;
  border-color: #f44336;
}
.open > .btn.btn-outline-danger.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-danger.dropdown-toggle.btn-icon:hover {
  background-color: #f44336;
}
.btn.btn-outline-danger.disabled:focus,
.btn.btn-outline-danger.disabled.focus,
.btn.btn-outline-danger:disabled:focus,
.btn.btn-outline-danger:disabled.focus {
  background-color: #fff !important;
  border-color: #f44336 !important;
}
.btn.btn-outline-danger.disabled:hover,
.btn.btn-outline-danger:disabled:hover {
  background-color: #fff !important;
  border-color: #f44336 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-danger {
  color: #fff;
  background-color: #f44336;
  border-color: #f44336;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-danger:hover,
.btn.btn-raised-danger:active {
  color: #fff !important;
  background-color: #f55a4e !important;
  border-color: #f55a4e !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-danger:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #f55a4e !important;
  border-color: #f55a4e !important;
}
.btn.btn-raised-danger.active,
.open > .btn.btn-raised-danger.dropdown-toggle,
.show > .btn.btn-raised-danger.dropdown-toggle {
  color: #fff;
  background-color: #f55a4e !important;
  border-color: #f55a4e !important;
}
.btn.btn-raised-danger.active:hover,
.btn.btn-raised-danger.active:focus,
.btn.btn-raised-danger.active.focus,
.open > .btn.btn-raised-danger.dropdown-toggle:hover,
.open > .btn.btn-raised-danger.dropdown-toggle:focus,
.open > .btn.btn-raised-danger.dropdown-toggle.focus,
.show > .btn.btn-raised-danger.dropdown-toggle:hover,
.show > .btn.btn-raised-danger.dropdown-toggle:focus,
.show > .btn.btn-raised-danger.dropdown-toggle.focus {
  color: #fff;
  background-color: #f55a4e;
  border-color: #f55a4e;
}
.open > .btn.btn-raised-danger.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #f44336;
}
.open > .btn.btn-raised-danger.dropdown-toggle.btn-icon:hover {
  background-color: #f55a4e;
}
.btn.btn-raised-danger.disabled:focus,
.btn.btn-raised-danger.disabled.focus,
.btn.btn-raised-danger:disabled:focus,
.btn.btn-raised-danger:disabled.focus {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
}
.btn.btn-raised-danger.disabled:hover,
.btn.btn-raised-danger:disabled:hover {
  background-color: #f44336 !important;
  border-color: #f44336 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-danger {
  color: #f44336 !important;
}
.btn.btn-link-danger::after {
  background-color: #f44336 !important;
}
.btn.btn-light {
  color: #fff;
  background-color: #666666;
  border-color: #666666;
  box-shadow: none;
}
.btn.btn-light:hover,
.btn.btn-light:active {
  color: #fff !important;
  background-color: #737373 !important;
  border-color: #737373 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-light:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #737373 !important;
  border-color: #737373 !important;
}
.btn.btn-light.active,
.open > .btn.btn-light.dropdown-toggle,
.show > .btn.btn-light.dropdown-toggle {
  color: #fff;
  background-color: #737373 !important;
  border-color: #737373 !important;
}
.btn.btn-light.active:hover,
.btn.btn-light.active:focus,
.btn.btn-light.active.focus,
.open > .btn.btn-light.dropdown-toggle:hover,
.open > .btn.btn-light.dropdown-toggle:focus,
.open > .btn.btn-light.dropdown-toggle.focus,
.show > .btn.btn-light.dropdown-toggle:hover,
.show > .btn.btn-light.dropdown-toggle:focus,
.show > .btn.btn-light.dropdown-toggle.focus {
  color: #fff;
  background-color: #737373;
  border-color: #737373;
}
.open > .btn.btn-light.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #666666;
}
.open > .btn.btn-light.dropdown-toggle.btn-icon:hover {
  background-color: #737373;
}
.btn.btn-light.disabled:focus,
.btn.btn-light.disabled.focus,
.btn.btn-light:disabled:focus,
.btn.btn-light:disabled.focus {
  background-color: #666666 !important;
  border-color: #666666 !important;
}
.btn.btn-light.disabled:hover,
.btn.btn-light:disabled:hover {
  background-color: #666666 !important;
  border-color: #666666 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-light {
  color: #666666;
  background-color: #fff;
  border-color: #666666;
  box-shadow: none;
}
.btn.btn-outline-light:hover,
.btn.btn-outline-light:active {
  color: #fff !important;
  background-color: #666666 !important;
  border-color: #666666 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-light:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #666666 !important;
  border-color: #666666 !important;
}
.btn.btn-outline-light.active,
.open > .btn.btn-outline-light.dropdown-toggle,
.show > .btn.btn-outline-light.dropdown-toggle {
  color: #fff;
  background-color: #666666 !important;
  border-color: #666666 !important;
}
.btn.btn-outline-light.active:hover,
.btn.btn-outline-light.active:focus,
.btn.btn-outline-light.active.focus,
.open > .btn.btn-outline-light.dropdown-toggle:hover,
.open > .btn.btn-outline-light.dropdown-toggle:focus,
.open > .btn.btn-outline-light.dropdown-toggle.focus,
.show > .btn.btn-outline-light.dropdown-toggle:hover,
.show > .btn.btn-outline-light.dropdown-toggle:focus,
.show > .btn.btn-outline-light.dropdown-toggle.focus {
  color: #fff;
  background-color: #666666;
  border-color: #666666;
}
.open > .btn.btn-outline-light.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-light.dropdown-toggle.btn-icon:hover {
  background-color: #666666;
}
.btn.btn-outline-light.disabled:focus,
.btn.btn-outline-light.disabled.focus,
.btn.btn-outline-light:disabled:focus,
.btn.btn-outline-light:disabled.focus {
  background-color: #fff !important;
  border-color: #666666 !important;
}
.btn.btn-outline-light.disabled:hover,
.btn.btn-outline-light:disabled:hover {
  background-color: #fff !important;
  border-color: #666666 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-light {
  color: #fff;
  background-color: #666666;
  border-color: #666666;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-light:hover,
.btn.btn-raised-light:active {
  color: #fff !important;
  background-color: #737373 !important;
  border-color: #737373 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-light:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #737373 !important;
  border-color: #737373 !important;
}
.btn.btn-raised-light.active,
.open > .btn.btn-raised-light.dropdown-toggle,
.show > .btn.btn-raised-light.dropdown-toggle {
  color: #fff;
  background-color: #737373 !important;
  border-color: #737373 !important;
}
.btn.btn-raised-light.active:hover,
.btn.btn-raised-light.active:focus,
.btn.btn-raised-light.active.focus,
.open > .btn.btn-raised-light.dropdown-toggle:hover,
.open > .btn.btn-raised-light.dropdown-toggle:focus,
.open > .btn.btn-raised-light.dropdown-toggle.focus,
.show > .btn.btn-raised-light.dropdown-toggle:hover,
.show > .btn.btn-raised-light.dropdown-toggle:focus,
.show > .btn.btn-raised-light.dropdown-toggle.focus {
  color: #fff;
  background-color: #737373;
  border-color: #737373;
}
.open > .btn.btn-raised-light.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #666666;
}
.open > .btn.btn-raised-light.dropdown-toggle.btn-icon:hover {
  background-color: #737373;
}
.btn.btn-raised-light.disabled:focus,
.btn.btn-raised-light.disabled.focus,
.btn.btn-raised-light:disabled:focus,
.btn.btn-raised-light:disabled.focus {
  background-color: #666666 !important;
  border-color: #666666 !important;
}
.btn.btn-raised-light.disabled:hover,
.btn.btn-raised-light:disabled:hover {
  background-color: #666666 !important;
  border-color: #666666 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-light {
  color: #666666 !important;
}
.btn.btn-link-light::after {
  background-color: #666666 !important;
}
.btn.btn-dark {
  color: #fff;
  background-color: #0a0a0a;
  border-color: #0a0a0a;
  box-shadow: none;
}
.btn.btn-dark:hover,
.btn.btn-dark:active {
  color: #fff !important;
  background-color: #171717 !important;
  border-color: #171717 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-dark:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #171717 !important;
  border-color: #171717 !important;
}
.btn.btn-dark.active,
.open > .btn.btn-dark.dropdown-toggle,
.show > .btn.btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #171717 !important;
  border-color: #171717 !important;
}
.btn.btn-dark.active:hover,
.btn.btn-dark.active:focus,
.btn.btn-dark.active.focus,
.open > .btn.btn-dark.dropdown-toggle:hover,
.open > .btn.btn-dark.dropdown-toggle:focus,
.open > .btn.btn-dark.dropdown-toggle.focus,
.show > .btn.btn-dark.dropdown-toggle:hover,
.show > .btn.btn-dark.dropdown-toggle:focus,
.show > .btn.btn-dark.dropdown-toggle.focus {
  color: #fff;
  background-color: #171717;
  border-color: #171717;
}
.open > .btn.btn-dark.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #0a0a0a;
}
.open > .btn.btn-dark.dropdown-toggle.btn-icon:hover {
  background-color: #171717;
}
.btn.btn-dark.disabled:focus,
.btn.btn-dark.disabled.focus,
.btn.btn-dark:disabled:focus,
.btn.btn-dark:disabled.focus {
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
}
.btn.btn-dark.disabled:hover,
.btn.btn-dark:disabled:hover {
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-outline-dark {
  color: #0a0a0a;
  background-color: #fff;
  border-color: #0a0a0a;
  box-shadow: none;
}
.btn.btn-outline-dark:hover,
.btn.btn-outline-dark:active {
  color: #fff !important;
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-outline-dark:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
}
.btn.btn-outline-dark.active,
.open > .btn.btn-outline-dark.dropdown-toggle,
.show > .btn.btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
}
.btn.btn-outline-dark.active:hover,
.btn.btn-outline-dark.active:focus,
.btn.btn-outline-dark.active.focus,
.open > .btn.btn-outline-dark.dropdown-toggle:hover,
.open > .btn.btn-outline-dark.dropdown-toggle:focus,
.open > .btn.btn-outline-dark.dropdown-toggle.focus,
.show > .btn.btn-outline-dark.dropdown-toggle:hover,
.show > .btn.btn-outline-dark.dropdown-toggle:focus,
.show > .btn.btn-outline-dark.dropdown-toggle.focus {
  color: #fff;
  background-color: #0a0a0a;
  border-color: #0a0a0a;
}
.open > .btn.btn-outline-dark.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-outline-dark.dropdown-toggle.btn-icon:hover {
  background-color: #0a0a0a;
}
.btn.btn-outline-dark.disabled:focus,
.btn.btn-outline-dark.disabled.focus,
.btn.btn-outline-dark:disabled:focus,
.btn.btn-outline-dark:disabled.focus {
  background-color: #fff !important;
  border-color: #0a0a0a !important;
}
.btn.btn-outline-dark.disabled:hover,
.btn.btn-outline-dark:disabled:hover {
  background-color: #fff !important;
  border-color: #0a0a0a !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-raised-dark {
  color: #fff;
  background-color: #0a0a0a;
  border-color: #0a0a0a;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-dark:hover,
.btn.btn-raised-dark:active {
  color: #fff !important;
  background-color: #171717 !important;
  border-color: #171717 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-dark:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #171717 !important;
  border-color: #171717 !important;
}
.btn.btn-raised-dark.active,
.open > .btn.btn-raised-dark.dropdown-toggle,
.show > .btn.btn-raised-dark.dropdown-toggle {
  color: #fff;
  background-color: #171717 !important;
  border-color: #171717 !important;
}
.btn.btn-raised-dark.active:hover,
.btn.btn-raised-dark.active:focus,
.btn.btn-raised-dark.active.focus,
.open > .btn.btn-raised-dark.dropdown-toggle:hover,
.open > .btn.btn-raised-dark.dropdown-toggle:focus,
.open > .btn.btn-raised-dark.dropdown-toggle.focus,
.show > .btn.btn-raised-dark.dropdown-toggle:hover,
.show > .btn.btn-raised-dark.dropdown-toggle:focus,
.show > .btn.btn-raised-dark.dropdown-toggle.focus {
  color: #fff;
  background-color: #171717;
  border-color: #171717;
}
.open > .btn.btn-raised-dark.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #0a0a0a;
}
.open > .btn.btn-raised-dark.dropdown-toggle.btn-icon:hover {
  background-color: #171717;
}
.btn.btn-raised-dark.disabled:focus,
.btn.btn-raised-dark.disabled.focus,
.btn.btn-raised-dark:disabled:focus,
.btn.btn-raised-dark:disabled.focus {
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
}
.btn.btn-raised-dark.disabled:hover,
.btn.btn-raised-dark:disabled:hover {
  background-color: #0a0a0a !important;
  border-color: #0a0a0a !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-link-dark {
  color: #0a0a0a !important;
}
.btn.btn-link-dark::after {
  background-color: #0a0a0a !important;
}
.btn.btn-default {
  background: #fff;
  color: black;
}
.btn.btn-default:hover {
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-default:focus,
.btn.btn-default:active {
  box-shadow: none;
}
.btn.btn-raised-default {
  background: #c5c9e4;
  color: black;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-raised-default:hover {
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-raised-default:focus,
.btn.btn-raised-default:active {
  box-shadow: none;
  border-color: #f9f9f9;
}
.btn.btn-facebook {
  color: #fff;
  background-color: #3765c9;
  border-color: #3765c9;
  box-shadow: none;
}
.btn.btn-facebook:hover,
.btn.btn-facebook:active {
  color: #fff !important;
  background-color: #4b74ce !important;
  border-color: #4b74ce !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-facebook:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #4b74ce !important;
  border-color: #4b74ce !important;
}
.btn.btn-facebook.active,
.open > .btn.btn-facebook.dropdown-toggle,
.show > .btn.btn-facebook.dropdown-toggle {
  color: #fff;
  background-color: #4b74ce !important;
  border-color: #4b74ce !important;
}
.btn.btn-facebook.active:hover,
.btn.btn-facebook.active:focus,
.btn.btn-facebook.active.focus,
.open > .btn.btn-facebook.dropdown-toggle:hover,
.open > .btn.btn-facebook.dropdown-toggle:focus,
.open > .btn.btn-facebook.dropdown-toggle.focus,
.show > .btn.btn-facebook.dropdown-toggle:hover,
.show > .btn.btn-facebook.dropdown-toggle:focus,
.show > .btn.btn-facebook.dropdown-toggle.focus {
  color: #fff;
  background-color: #4b74ce;
  border-color: #4b74ce;
}
.open > .btn.btn-facebook.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #3765c9;
}
.open > .btn.btn-facebook.dropdown-toggle.btn-icon:hover {
  background-color: #4b74ce;
}
.btn.btn-facebook.disabled:focus,
.btn.btn-facebook.disabled.focus,
.btn.btn-facebook:disabled:focus,
.btn.btn-facebook:disabled.focus {
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
}
.btn.btn-facebook.disabled:hover,
.btn.btn-facebook:disabled:hover {
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-facebook.btn-outline {
  color: #3765c9;
  background-color: #fff;
  border-color: #3765c9;
  box-shadow: none;
}
.btn.btn-facebook.btn-outline:hover,
.btn.btn-facebook.btn-outline:active {
  color: #fff !important;
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-facebook.btn-outline:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
}
.btn.btn-facebook.btn-outline.active,
.open > .btn.btn-facebook.btn-outline.dropdown-toggle,
.show > .btn.btn-facebook.btn-outline.dropdown-toggle {
  color: #fff;
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
}
.btn.btn-facebook.btn-outline.active:hover,
.btn.btn-facebook.btn-outline.active:focus,
.btn.btn-facebook.btn-outline.active.focus,
.open > .btn.btn-facebook.btn-outline.dropdown-toggle:hover,
.open > .btn.btn-facebook.btn-outline.dropdown-toggle:focus,
.open > .btn.btn-facebook.btn-outline.dropdown-toggle.focus,
.show > .btn.btn-facebook.btn-outline.dropdown-toggle:hover,
.show > .btn.btn-facebook.btn-outline.dropdown-toggle:focus,
.show > .btn.btn-facebook.btn-outline.dropdown-toggle.focus {
  color: #fff;
  background-color: #3765c9;
  border-color: #3765c9;
}
.open > .btn.btn-facebook.btn-outline.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-facebook.btn-outline.dropdown-toggle.btn-icon:hover {
  background-color: #3765c9;
}
.btn.btn-facebook.btn-outline.disabled:focus,
.btn.btn-facebook.btn-outline.disabled.focus,
.btn.btn-facebook.btn-outline:disabled:focus,
.btn.btn-facebook.btn-outline:disabled.focus {
  background-color: #fff !important;
  border-color: #3765c9 !important;
}
.btn.btn-facebook.btn-outline.disabled:hover,
.btn.btn-facebook.btn-outline:disabled:hover {
  background-color: #fff !important;
  border-color: #3765c9 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-facebook.btn-raised {
  color: #fff;
  background-color: #3765c9;
  border-color: #3765c9;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-facebook.btn-raised:hover,
.btn.btn-facebook.btn-raised:active {
  color: #fff !important;
  background-color: #4b74ce !important;
  border-color: #4b74ce !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-facebook.btn-raised:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #4b74ce !important;
  border-color: #4b74ce !important;
}
.btn.btn-facebook.btn-raised.active,
.open > .btn.btn-facebook.btn-raised.dropdown-toggle,
.show > .btn.btn-facebook.btn-raised.dropdown-toggle {
  color: #fff;
  background-color: #4b74ce !important;
  border-color: #4b74ce !important;
}
.btn.btn-facebook.btn-raised.active:hover,
.btn.btn-facebook.btn-raised.active:focus,
.btn.btn-facebook.btn-raised.active.focus,
.open > .btn.btn-facebook.btn-raised.dropdown-toggle:hover,
.open > .btn.btn-facebook.btn-raised.dropdown-toggle:focus,
.open > .btn.btn-facebook.btn-raised.dropdown-toggle.focus,
.show > .btn.btn-facebook.btn-raised.dropdown-toggle:hover,
.show > .btn.btn-facebook.btn-raised.dropdown-toggle:focus,
.show > .btn.btn-facebook.btn-raised.dropdown-toggle.focus {
  color: #fff;
  background-color: #4b74ce;
  border-color: #4b74ce;
}
.open > .btn.btn-facebook.btn-raised.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #3765c9;
}
.open > .btn.btn-facebook.btn-raised.dropdown-toggle.btn-icon:hover {
  background-color: #4b74ce;
}
.btn.btn-facebook.btn-raised.disabled:focus,
.btn.btn-facebook.btn-raised.disabled.focus,
.btn.btn-facebook.btn-raised:disabled:focus,
.btn.btn-facebook.btn-raised:disabled.focus {
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
}
.btn.btn-facebook.btn-raised.disabled:hover,
.btn.btn-facebook.btn-raised:disabled:hover {
  background-color: #3765c9 !important;
  border-color: #3765c9 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-google {
  color: #fff;
  background-color: #ec412c;
  border-color: #ec412c;
  box-shadow: none;
}
.btn.btn-google:hover,
.btn.btn-google:active {
  color: #fff !important;
  background-color: #ee5643 !important;
  border-color: #ee5643 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-google:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #ee5643 !important;
  border-color: #ee5643 !important;
}
.btn.btn-google.active,
.open > .btn.btn-google.dropdown-toggle,
.show > .btn.btn-google.dropdown-toggle {
  color: #fff;
  background-color: #ee5643 !important;
  border-color: #ee5643 !important;
}
.btn.btn-google.active:hover,
.btn.btn-google.active:focus,
.btn.btn-google.active.focus,
.open > .btn.btn-google.dropdown-toggle:hover,
.open > .btn.btn-google.dropdown-toggle:focus,
.open > .btn.btn-google.dropdown-toggle.focus,
.show > .btn.btn-google.dropdown-toggle:hover,
.show > .btn.btn-google.dropdown-toggle:focus,
.show > .btn.btn-google.dropdown-toggle.focus {
  color: #fff;
  background-color: #ee5643;
  border-color: #ee5643;
}
.open > .btn.btn-google.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #ec412c;
}
.open > .btn.btn-google.dropdown-toggle.btn-icon:hover {
  background-color: #ee5643;
}
.btn.btn-google.disabled:focus,
.btn.btn-google.disabled.focus,
.btn.btn-google:disabled:focus,
.btn.btn-google:disabled.focus {
  background-color: #ec412c !important;
  border-color: #ec412c !important;
}
.btn.btn-google.disabled:hover,
.btn.btn-google:disabled:hover {
  background-color: #ec412c !important;
  border-color: #ec412c !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-google.btn-outline {
  color: #ec412c;
  background-color: #fff;
  border-color: #ec412c;
  box-shadow: none;
}
.btn.btn-google.btn-outline:hover,
.btn.btn-google.btn-outline:active {
  color: #fff !important;
  background-color: #ec412c !important;
  border-color: #ec412c !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-google.btn-outline:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #ec412c !important;
  border-color: #ec412c !important;
}
.btn.btn-google.btn-outline.active,
.open > .btn.btn-google.btn-outline.dropdown-toggle,
.show > .btn.btn-google.btn-outline.dropdown-toggle {
  color: #fff;
  background-color: #ec412c !important;
  border-color: #ec412c !important;
}
.btn.btn-google.btn-outline.active:hover,
.btn.btn-google.btn-outline.active:focus,
.btn.btn-google.btn-outline.active.focus,
.open > .btn.btn-google.btn-outline.dropdown-toggle:hover,
.open > .btn.btn-google.btn-outline.dropdown-toggle:focus,
.open > .btn.btn-google.btn-outline.dropdown-toggle.focus,
.show > .btn.btn-google.btn-outline.dropdown-toggle:hover,
.show > .btn.btn-google.btn-outline.dropdown-toggle:focus,
.show > .btn.btn-google.btn-outline.dropdown-toggle.focus {
  color: #fff;
  background-color: #ec412c;
  border-color: #ec412c;
}
.open > .btn.btn-google.btn-outline.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-google.btn-outline.dropdown-toggle.btn-icon:hover {
  background-color: #ec412c;
}
.btn.btn-google.btn-outline.disabled:focus,
.btn.btn-google.btn-outline.disabled.focus,
.btn.btn-google.btn-outline:disabled:focus,
.btn.btn-google.btn-outline:disabled.focus {
  background-color: #fff !important;
  border-color: #ec412c !important;
}
.btn.btn-google.btn-outline.disabled:hover,
.btn.btn-google.btn-outline:disabled:hover {
  background-color: #fff !important;
  border-color: #ec412c !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-google.btn-raised {
  color: #fff;
  background-color: #ec412c;
  border-color: #ec412c;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-google.btn-raised:hover,
.btn.btn-google.btn-raised:active {
  color: #fff !important;
  background-color: #ee5643 !important;
  border-color: #ee5643 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-google.btn-raised:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #ee5643 !important;
  border-color: #ee5643 !important;
}
.btn.btn-google.btn-raised.active,
.open > .btn.btn-google.btn-raised.dropdown-toggle,
.show > .btn.btn-google.btn-raised.dropdown-toggle {
  color: #fff;
  background-color: #ee5643 !important;
  border-color: #ee5643 !important;
}
.btn.btn-google.btn-raised.active:hover,
.btn.btn-google.btn-raised.active:focus,
.btn.btn-google.btn-raised.active.focus,
.open > .btn.btn-google.btn-raised.dropdown-toggle:hover,
.open > .btn.btn-google.btn-raised.dropdown-toggle:focus,
.open > .btn.btn-google.btn-raised.dropdown-toggle.focus,
.show > .btn.btn-google.btn-raised.dropdown-toggle:hover,
.show > .btn.btn-google.btn-raised.dropdown-toggle:focus,
.show > .btn.btn-google.btn-raised.dropdown-toggle.focus {
  color: #fff;
  background-color: #ee5643;
  border-color: #ee5643;
}
.open > .btn.btn-google.btn-raised.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #ec412c;
}
.open > .btn.btn-google.btn-raised.dropdown-toggle.btn-icon:hover {
  background-color: #ee5643;
}
.btn.btn-google.btn-raised.disabled:focus,
.btn.btn-google.btn-raised.disabled.focus,
.btn.btn-google.btn-raised:disabled:focus,
.btn.btn-google.btn-raised:disabled.focus {
  background-color: #ec412c !important;
  border-color: #ec412c !important;
}
.btn.btn-google.btn-raised.disabled:hover,
.btn.btn-google.btn-raised:disabled:hover {
  background-color: #ec412c !important;
  border-color: #ec412c !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-twitter {
  color: #fff;
  background-color: #039ff5;
  border-color: #039ff5;
  box-shadow: none;
}
.btn.btn-twitter:hover,
.btn.btn-twitter:active {
  color: #fff !important;
  background-color: #15aafc !important;
  border-color: #15aafc !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-twitter:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #15aafc !important;
  border-color: #15aafc !important;
}
.btn.btn-twitter.active,
.open > .btn.btn-twitter.dropdown-toggle,
.show > .btn.btn-twitter.dropdown-toggle {
  color: #fff;
  background-color: #15aafc !important;
  border-color: #15aafc !important;
}
.btn.btn-twitter.active:hover,
.btn.btn-twitter.active:focus,
.btn.btn-twitter.active.focus,
.open > .btn.btn-twitter.dropdown-toggle:hover,
.open > .btn.btn-twitter.dropdown-toggle:focus,
.open > .btn.btn-twitter.dropdown-toggle.focus,
.show > .btn.btn-twitter.dropdown-toggle:hover,
.show > .btn.btn-twitter.dropdown-toggle:focus,
.show > .btn.btn-twitter.dropdown-toggle.focus {
  color: #fff;
  background-color: #15aafc;
  border-color: #15aafc;
}
.open > .btn.btn-twitter.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #039ff5;
}
.open > .btn.btn-twitter.dropdown-toggle.btn-icon:hover {
  background-color: #15aafc;
}
.btn.btn-twitter.disabled:focus,
.btn.btn-twitter.disabled.focus,
.btn.btn-twitter:disabled:focus,
.btn.btn-twitter:disabled.focus {
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
}
.btn.btn-twitter.disabled:hover,
.btn.btn-twitter:disabled:hover {
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-twitter.btn-outline {
  color: #039ff5;
  background-color: #fff;
  border-color: #039ff5;
  box-shadow: none;
}
.btn.btn-twitter.btn-outline:hover,
.btn.btn-twitter.btn-outline:active {
  color: #fff !important;
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-twitter.btn-outline:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
}
.btn.btn-twitter.btn-outline.active,
.open > .btn.btn-twitter.btn-outline.dropdown-toggle,
.show > .btn.btn-twitter.btn-outline.dropdown-toggle {
  color: #fff;
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
}
.btn.btn-twitter.btn-outline.active:hover,
.btn.btn-twitter.btn-outline.active:focus,
.btn.btn-twitter.btn-outline.active.focus,
.open > .btn.btn-twitter.btn-outline.dropdown-toggle:hover,
.open > .btn.btn-twitter.btn-outline.dropdown-toggle:focus,
.open > .btn.btn-twitter.btn-outline.dropdown-toggle.focus,
.show > .btn.btn-twitter.btn-outline.dropdown-toggle:hover,
.show > .btn.btn-twitter.btn-outline.dropdown-toggle:focus,
.show > .btn.btn-twitter.btn-outline.dropdown-toggle.focus {
  color: #fff;
  background-color: #039ff5;
  border-color: #039ff5;
}
.open > .btn.btn-twitter.btn-outline.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #fff;
}
.open > .btn.btn-twitter.btn-outline.dropdown-toggle.btn-icon:hover {
  background-color: #039ff5;
}
.btn.btn-twitter.btn-outline.disabled:focus,
.btn.btn-twitter.btn-outline.disabled.focus,
.btn.btn-twitter.btn-outline:disabled:focus,
.btn.btn-twitter.btn-outline:disabled.focus {
  background-color: #fff !important;
  border-color: #039ff5 !important;
}
.btn.btn-twitter.btn-outline.disabled:hover,
.btn.btn-twitter.btn-outline:disabled:hover {
  background-color: #fff !important;
  border-color: #039ff5 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-twitter.btn-raised {
  color: #fff;
  background-color: #039ff5;
  border-color: #039ff5;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.btn.btn-twitter.btn-raised:hover,
.btn.btn-twitter.btn-raised:active {
  color: #fff !important;
  background-color: #15aafc !important;
  border-color: #15aafc !important;
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.btn.btn-twitter.btn-raised:focus {
  color: #fff !important;
  box-shadow: none !important;
  background-color: #15aafc !important;
  border-color: #15aafc !important;
}
.btn.btn-twitter.btn-raised.active,
.open > .btn.btn-twitter.btn-raised.dropdown-toggle,
.show > .btn.btn-twitter.btn-raised.dropdown-toggle {
  color: #fff;
  background-color: #15aafc !important;
  border-color: #15aafc !important;
}
.btn.btn-twitter.btn-raised.active:hover,
.btn.btn-twitter.btn-raised.active:focus,
.btn.btn-twitter.btn-raised.active.focus,
.open > .btn.btn-twitter.btn-raised.dropdown-toggle:hover,
.open > .btn.btn-twitter.btn-raised.dropdown-toggle:focus,
.open > .btn.btn-twitter.btn-raised.dropdown-toggle.focus,
.show > .btn.btn-twitter.btn-raised.dropdown-toggle:hover,
.show > .btn.btn-twitter.btn-raised.dropdown-toggle:focus,
.show > .btn.btn-twitter.btn-raised.dropdown-toggle.focus {
  color: #fff;
  background-color: #15aafc;
  border-color: #15aafc;
}
.open > .btn.btn-twitter.btn-raised.dropdown-toggle.btn-icon {
  color: inherit;
  background-color: #039ff5;
}
.open > .btn.btn-twitter.btn-raised.dropdown-toggle.btn-icon:hover {
  background-color: #15aafc;
}
.btn.btn-twitter.btn-raised.disabled:focus,
.btn.btn-twitter.btn-raised.disabled.focus,
.btn.btn-twitter.btn-raised:disabled:focus,
.btn.btn-twitter.btn-raised:disabled.focus {
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
}
.btn.btn-twitter.btn-raised.disabled:hover,
.btn.btn-twitter.btn-raised:disabled:hover {
  background-color: #039ff5 !important;
  border-color: #039ff5 !important;
  box-shadow: none !important;
  transform: none !important;
}
.btn.btn-sm {
  font-size: 0.75rem;
  padding: 0.3125rem 0.875rem;
}
.btn.btn-lg {
  font-size: 1.125rem;
  padding: 0.65625rem 1.875rem;
}
.btn.btn-icon.btn-sm {
  height: 36px;
  width: 36px;
  padding: 0;
}
.btn.btn-icon.btn-sm .icon,
.btn.btn-icon.btn-sm i,
.btn.btn-icon.btn-sm [class^="ti-"],
.btn.btn-icon.btn-sm [class*=" ti-"] {
  font-size: 0.75rem;
}
.btn.btn-icon.btn-lg {
  height: 56px;
  width: 56px;
  padding: 0;
}
.btn.btn-icon.btn-lg .icon,
.btn.btn-icon.btn-lg i,
.btn.btn-icon.btn-lg [class^="ti-"],
.btn.btn-icon.btn-lg [class*=" ti-"] {
  font-size: 1rem;
}
.btn .icon,
.btn i,
.btn [class^="ti-"],
.btn [class*=" ti-"] {
  font-size: 0.875rem;
  margin-right: 0.4375rem;
}
.btn .feather {
  height: 16px;
  width: 16px;
  margin-right: 0.4375rem;
}
.btn.btn-link {
  position: relative;
  background-color: transparent;
  padding: 0.2rem 0.3rem;
}
.btn.btn-link::after {
  position: absolute;
  content: "";
  bottom: 0;
  width: 0;
  left: 0;
  height: 0.125rem;
  transition: all 0.2s ease-in-out !important;
}
.btn.btn-link:hover {
  text-decoration: none;
}
.btn.btn-link:hover::after {
  width: 100%;
}
.btn.rounded,
.btn.btn-rounded {
  border-radius: 2.5rem !important;
}
.btn.semi-rounded,
.btn.btn-semi-rounded {
  border-radius: 0.625rem !important;
}
.btn.move-up:hover,
.btn.move-up:active {
  transform: translateY(-2px);
}
.btn:not(:last-child) {
  margin-right: 0.5rem;
}

.btn-group .btn {
  transform: none !important;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  padding: 0;
}
.btn-icon .icon,
.btn-icon i,
.btn-icon [class^="ti-"],
.btn-icon [class*=" ti-"] {
  font-size: 0.875rem;
  margin-right: 0.4375rem;
}
.btn-icon .feather {
  height: 16px;
  width: 16px;
  margin-right: 0.4375rem;
}
.btn-icon .icon,
.btn-icon i,
.btn-icon [class^="ti-"],
.btn-icon [class*=" ti-"],
.btn-icon .feather {
  margin: 0;
}

.badge {
  padding: 0.3em 0.4em;
  font-weight: 500;
}
.badge.badge-white {
  background: #fff;
  color: #0a0a0a;
}

.badge-primary {
  color: #fff;
  background-color: #8b5cf6;
}
.badge-primary[href] {
  color: #fff;
}
.badge-primary[href]:hover,
.badge-primary[href]:focus {
  background-color: #4d2673;
}

.badge-secondary {
  color: #fff;
  background-color: #1f1f1f;
}
.badge-secondary[href] {
  color: #fff;
}
.badge-secondary[href]:hover,
.badge-secondary[href]:focus {
  background-color: #050505;
}

.badge-success {
  color: #fff;
  background-color: #4caf50;
}
.badge-success[href] {
  color: #fff;
}
.badge-success[href]:hover,
.badge-success[href]:focus {
  background-color: #3d8b40;
}

.badge-info {
  color: #fff;
  background-color: #003473;
}
.badge-info[href] {
  color: #fff;
}
.badge-info[href]:hover,
.badge-info[href]:focus {
  background-color: #001d40;
}

.badge-warning {
  color: #fff;
  background-color: #e97d23;
}
.badge-warning[href] {
  color: #fff;
}
.badge-warning[href]:hover,
.badge-warning[href]:focus {
  background-color: #c56414;
}

.badge-danger {
  color: #fff;
  background-color: #f44336;
}
.badge-danger[href] {
  color: #fff;
}
.badge-danger[href]:hover,
.badge-danger[href]:focus {
  background-color: #ea1c0d;
}

.badge-light {
  color: #fff;
  background-color: #666666;
}
.badge-light[href] {
  color: #fff;
}
.badge-light[href]:hover,
.badge-light[href]:focus {
  background-color: #4d4d4d;
}

.badge-dark {
  color: #fff;
  background-color: #0a0a0a;
}
.badge-dark[href] {
  color: #fff;
}
.badge-dark[href]:hover,
.badge-dark[href]:focus {
  background-color: black;
}

[class^="icon-light-bg-"],
[class*=" icon-light-bg-"] {
  display: inline-block;
  height: 2.5rem;
  width: 2.5rem;
  text-align: center;
  padding: 0.625rem 0.375rem;
  border-radius: 0.25rem;
}

.icon-light-bg-primary {
  background-color: rgba(102, 51, 153, 0.2);
  color: #4d2673;
}

.icon-light-bg-secondary {
  background-color: rgba(31, 31, 31, 0.2);
  color: #050505;
}

.icon-light-bg-success {
  background-color: rgba(76, 175, 80, 0.2);
  color: #3d8b40;
}

.icon-light-bg-info {
  background-color: rgba(0, 52, 115, 0.2);
  color: #001d40;
}

.icon-light-bg-warning {
  background-color: rgba(233, 125, 35, 0.2);
  color: #c56414;
}

.icon-light-bg-danger {
  background-color: rgba(244, 67, 54, 0.2);
  color: #ea1c0d;
}

.icon-light-bg-light {
  background-color: rgba(102, 102, 102, 0.2);
  color: #4d4d4d;
}

.icon-light-bg-dark {
  background-color: rgba(10, 10, 10, 0.2);
  color: black;
}

.close {
  text-shadow: none;
}
.close i {
  font-size: 1rem;
}
.alert-primary .close {
  color: #fff;
}
.alert-secondary .close {
  color: #fff;
}
.alert-success .close {
  color: #fff;
}
.alert-info .close {
  color: #fff;
}
.alert-warning .close {
  color: #fff;
}
.alert-danger .close {
  color: #fff;
}
.alert-light .close {
  color: #fff;
}
.alert-dark .close {
  color: #fff;
}

.form-control {
  font-size: 0.875rem;
  height: 2.5rem;
  border-color: #999999;
  border-width: 1px;
  background: transparent;
  transition: all 0.2s ease-in-out !important;
}
.form-control:focus,
.form-control .focus {
  border-color: #8b5cf6;
  box-shadow: none;
}
.input-rounded .form-control,
.form-control.input-rounded {
  border-radius: 1.875rem;
}
.input-light .form-control,
.form-control.input-light {
  border: 0 !important;
  background: #fff;
}
.has-success .form-control,
.has-error .form-control,
.form-control .has-warning {
  padding-right: 48px;
}
.has-success .form-control {
  border-color: #4caf50;
  color: #4caf50;
}
.has-warning .form-control {
  border-color: #e97d23;
  color: #e97d23;
}
.has-error .form-control {
  border-color: #f44336;
  color: #f44336;
}
.input-group-prepend + .form-control {
  border-left: 0;
}
.input-group-append + .form-control {
  border-right: 0;
}
.form-control::-moz-placeholder {
  color: #1f1f1f;
  opacity: 0.6;
}
.form-control:-moz-placeholder {
  color: #1f1f1f;
  opacity: 0.6;
}
.form-control::-webkit-input-placeholder {
  color: #1f1f1f;
  opacity: 0.6;
}
.form-control:-ms-input-placeholder {
  color: #1f1f1f;
  opacity: 0.6;
}

.input-group {
  border-radius: 0.25rem;
  transition: all 0.2s ease-in-out !important;
}
.input-group.has-success,
.input-group.has-error,
.input-group.has-warning {
  position: relative;
  overflow: hidden;
}
.input-group.has-success::after,
.input-group.has-error::after,
.input-group.has-warning::after {
  position: absolute;
  top: calc(50% - 0.8125rem / 2);
  right: 0px;
  font-size: 0.8125rem;
  font-family: "themify";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  z-index: 10;
  width: 40px;
  text-align: center;
}
.input-group.has-success::after {
  content: "\e64c";
  color: #4caf50;
}
.input-group.has-error::after {
  content: "\e646";
  color: #f44336;
}
.input-group.has-warning::after {
  content: "\e646";
  color: #e97d23;
}
.input-group .icon,
.input-group i,
.input-group [class^="ti-"],
.input-group [class*=" ti-"] {
  color: #525252;
}
.input-group.input-light {
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
}
.input-rounded .input-group,
.input-group.input-rounded {
  border-radius: 1.875rem;
}
.input-group.input-group-focus {
  box-shadow: 0 14px 26px -12px rgba(82, 82, 82, 0.42),
    0 4px 23px 0px rgba(82, 82, 82, 0.12), 0 8px 10px -5px rgba(82, 82, 82, 0.2);
}
.input-group.input-group-focus .input-group-text {
  border-color: #8b5cf6;
  color: #8b5cf6;
  background: #fff;
}
.input-group.input-group-focus .form-control {
  background: #fff;
}
.input-group.input-group-focus.has-success .input-group-text {
  color: #4caf50;
}
.input-group.input-group-focus.has-error .input-group-text {
  color: #f44336;
}
.input-group.input-group-focus.has-warning .input-group-text {
  color: #e97d23;
}

.input-group-text {
  font-size: 0.875rem;
  border-color: #999999;
  border-width: 1px;
  background: transparent;
  border-right: 0;
  transition: all 0.2s ease-in-out !important;
}
.input-light .input-group-text,
.input-group-text.input-light {
  border: 0 !important;
  background: #fff;
}
.input-rounded .input-group-text,
.input-group-text.input-rounded {
  border-radius: 1.875rem;
}
.has-success .input-group-text,
.input-group-foucs.has-success .input-group-text {
  border-color: #4caf50 !important;
  color: #4caf50;
}
.has-warning .input-group-text,
.input-group-foucs.has-error .input-group-text {
  border-color: #e97d23 !important;
  color: #e97d23;
}
.has-error .input-group-text,
.input-group-foucs.has-warning .input-group-text {
  border-color: #f44336 !important;
  color: #f44336;
}
.input-group-prepend .input-group-text {
  padding-right: 1px !important;
}

.custom-checkbox .custom-control-label::before,
.custom-checkbox .custom-control-label::after,
.custom-radio .custom-control-label::before,
.custom-radio .custom-control-label::after {
  top: calc(50% - 1.25rem / 2);
  height: 1.25rem;
  width: 1.25rem;
}

.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before,
.custom-radio .custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(102, 51, 153, 0.4);
  border-color: #8b5cf6;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before,
.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
  background: #8b5cf6;
  border-color: #8b5cf6;
}

.custom-checkbox.checkbox-primary .custom-control-label::before,
.custom-checkbox.radio-primary .custom-control-label::before,
.custom-radio.checkbox-primary .custom-control-label::before,
.custom-radio.radio-primary .custom-control-label::before {
  border-color: #8b5cf6;
}

.custom-checkbox.checkbox-primary
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-primary
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-primary
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-primary
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(102, 51, 153, 0.4);
  border-color: #8b5cf6;
}

.custom-checkbox.checkbox-primary
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-primary
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-primary
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-primary
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #8b5cf6;
  border-color: #8b5cf6;
}

.custom-checkbox.checkbox-secondary .custom-control-label::before,
.custom-checkbox.radio-secondary .custom-control-label::before,
.custom-radio.checkbox-secondary .custom-control-label::before,
.custom-radio.radio-secondary .custom-control-label::before {
  border-color: #1f1f1f;
}

.custom-checkbox.checkbox-secondary
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-secondary
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-secondary
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-secondary
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(31, 31, 31, 0.4);
  border-color: #1f1f1f;
}

.custom-checkbox.checkbox-secondary
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-secondary
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-secondary
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-secondary
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #1f1f1f;
  border-color: #1f1f1f;
}

.custom-checkbox.checkbox-success .custom-control-label::before,
.custom-checkbox.radio-success .custom-control-label::before,
.custom-radio.checkbox-success .custom-control-label::before,
.custom-radio.radio-success .custom-control-label::before {
  border-color: #4caf50;
}

.custom-checkbox.checkbox-success
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-success
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-success
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-success
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.4);
  border-color: #4caf50;
}

.custom-checkbox.checkbox-success
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-success
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-success
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-success
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #4caf50;
  border-color: #4caf50;
}

.custom-checkbox.checkbox-info .custom-control-label::before,
.custom-checkbox.radio-info .custom-control-label::before,
.custom-radio.checkbox-info .custom-control-label::before,
.custom-radio.radio-info .custom-control-label::before {
  border-color: #003473;
}

.custom-checkbox.checkbox-info
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-info
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-info
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-info
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(0, 52, 115, 0.4);
  border-color: #003473;
}

.custom-checkbox.checkbox-info
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-info
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-info
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-info
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #003473;
  border-color: #003473;
}

.custom-checkbox.checkbox-warning .custom-control-label::before,
.custom-checkbox.radio-warning .custom-control-label::before,
.custom-radio.checkbox-warning .custom-control-label::before,
.custom-radio.radio-warning .custom-control-label::before {
  border-color: #e97d23;
}

.custom-checkbox.checkbox-warning
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-warning
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-warning
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-warning
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(233, 125, 35, 0.4);
  border-color: #e97d23;
}

.custom-checkbox.checkbox-warning
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-warning
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-warning
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-warning
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #e97d23;
  border-color: #e97d23;
}

.custom-checkbox.checkbox-danger .custom-control-label::before,
.custom-checkbox.radio-danger .custom-control-label::before,
.custom-radio.checkbox-danger .custom-control-label::before,
.custom-radio.radio-danger .custom-control-label::before {
  border-color: #f44336;
}

.custom-checkbox.checkbox-danger
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-danger
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-danger
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-danger
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(244, 67, 54, 0.4);
  border-color: #f44336;
}

.custom-checkbox.checkbox-danger
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-danger
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-danger
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-danger
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #f44336;
  border-color: #f44336;
}

.custom-checkbox.checkbox-light .custom-control-label::before,
.custom-checkbox.radio-light .custom-control-label::before,
.custom-radio.checkbox-light .custom-control-label::before,
.custom-radio.radio-light .custom-control-label::before {
  border-color: #666666;
}

.custom-checkbox.checkbox-light
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-light
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-light
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-light
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(102, 102, 102, 0.4);
  border-color: #666666;
}

.custom-checkbox.checkbox-light
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-light
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-light
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-light
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #666666;
  border-color: #666666;
}

.custom-checkbox.checkbox-dark .custom-control-label::before,
.custom-checkbox.radio-dark .custom-control-label::before,
.custom-radio.checkbox-dark .custom-control-label::before,
.custom-radio.radio-dark .custom-control-label::before {
  border-color: #0a0a0a;
}

.custom-checkbox.checkbox-dark
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-checkbox.radio-dark
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.checkbox-dark
  .custom-control-input:focus
  ~ .custom-control-label::before,
.custom-radio.radio-dark
  .custom-control-input:focus
  ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(10, 10, 10, 0.4);
  border-color: #0a0a0a;
}

.custom-checkbox.checkbox-dark
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-checkbox.radio-dark
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.checkbox-dark
  .custom-control-input:checked
  ~ .custom-control-label::before,
.custom-radio.radio-dark
  .custom-control-input:checked
  ~ .custom-control-label::before {
  background: #0a0a0a;
  border-color: #0a0a0a;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 2px;
}

input[disabled] + .custom-control-label::before {
  opacity: 0.25;
}

.switch {
  position: relative;
  display: inline-block;
  padding-left: 3.5rem;
  height: 1rem;
}
.switch span:not(.slider) {
  position: relative;
  top: -2px;
  cursor: pointer;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.switch .slider {
  position: absolute;
  cursor: pointer;
  width: 2.625rem;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 1.875rem;
  background-color: #999999;
  transition: all 0.2s ease-in-out !important;
}
.switch .slider:before {
  position: absolute;
  content: "";
  height: 1.5rem;
  width: 1.5rem;
  left: -0.125rem;
  bottom: -0.25rem;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 3px 1px -2px rgba(82, 82, 82, 0.2),
    0 2px 2px 0 rgba(82, 82, 82, 0.14), 0 1px 5px 0 rgba(82, 82, 82, 0.12);
  transition: all 0.2s ease-in-out !important;
}
.switch input:checked + .slider {
  background-color: #8b5cf6;
}
.switch input:focus + .slider {
  box-shadow: 0 0 1px #8b5cf6;
}
.switch input:checked + .slider:before {
  transform: translateX(1.5rem);
}

.switch-primary input:checked + .slider {
  background-color: #8b5cf6;
}

.switch-primary input:focus + .slider {
  box-shadow: 0 0 1px #8b5cf6;
}

.switch-secondary input:checked + .slider {
  background-color: #1f1f1f;
}

.switch-secondary input:focus + .slider {
  box-shadow: 0 0 1px #1f1f1f;
}

.switch-success input:checked + .slider {
  background-color: #4caf50;
}

.switch-success input:focus + .slider {
  box-shadow: 0 0 1px #4caf50;
}

.switch-info input:checked + .slider {
  background-color: #003473;
}

.switch-info input:focus + .slider {
  box-shadow: 0 0 1px #003473;
}

.switch-warning input:checked + .slider {
  background-color: #e97d23;
}

.switch-warning input:focus + .slider {
  box-shadow: 0 0 1px #e97d23;
}

.switch-danger input:checked + .slider {
  background-color: #f44336;
}

.switch-danger input:focus + .slider {
  box-shadow: 0 0 1px #f44336;
}

.switch-light input:checked + .slider {
  background-color: #666666;
}

.switch-light input:focus + .slider {
  box-shadow: 0 0 1px #666666;
}

.switch-dark input:checked + .slider {
  background-color: #0a0a0a;
}

.switch-dark input:focus + .slider {
  box-shadow: 0 0 1px #0a0a0a;
}

input[disabled] + .slider,
input[disabled] + .slider {
  opacity: 0.4;
}
input[disabled] + .slider::before,
input[disabled] + .slider::before {
  background-color: #666666;
  box-shadow: 0 3px 1px -2px rgba(102, 102, 102, 0.2),
    0 2px 2px 0 rgba(102, 102, 102, 0.14), 0 1px 5px 0 rgba(102, 102, 102, 0.12);
}

.dropdown-menu {
  border: 0;
  font-size: 1rem;
  padding: 0;
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}
.dropdown-menu .dropdown-header {
  color: #525252;
  font-weight: 500;
  padding: 0.75rem 1.5rem 0.5rem;
}
.dropdown-menu .dropdown-item {
  padding: 0.5rem 1.5rem;
}
.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item:focus,
.dropdown-menu .dropdown-item:active {
  background-color: #6e37a4;
  color: #fff;
  outline: 0;
}
.dropdown-menu .dropdown-item.active {
  background-color: #6e37a4;
  color: #fff;
}

.nav-tabs-primary .tab-content,
.nav-tabs-primary .nav-tabs {
  border-color: #8b5cf6 !important;
}

.nav-tabs-primary .nav-link.active {
  border-color: #8b5cf6 #8b5cf6 #fff !important;
  color: #8b5cf6 !important;
}

.nav-tabs-primary .nav-link:hover {
  color: #8b5cf6;
}

.nav-pills-primary .nav-link.active,
.nav-pills-primary .show > .nav-link {
  background-color: #8b5cf6;
}

.nav-tabs-secondary .tab-content,
.nav-tabs-secondary .nav-tabs {
  border-color: #1f1f1f !important;
}

.nav-tabs-secondary .nav-link.active {
  border-color: #1f1f1f #1f1f1f #fff !important;
  color: #1f1f1f !important;
}

.nav-tabs-secondary .nav-link:hover {
  color: #1f1f1f;
}

.nav-pills-secondary .nav-link.active,
.nav-pills-secondary .show > .nav-link {
  background-color: #1f1f1f;
}

.nav-tabs-success .tab-content,
.nav-tabs-success .nav-tabs {
  border-color: #4caf50 !important;
}

.nav-tabs-success .nav-link.active {
  border-color: #4caf50 #4caf50 #fff !important;
  color: #4caf50 !important;
}

.nav-tabs-success .nav-link:hover {
  color: #4caf50;
}

.nav-pills-success .nav-link.active,
.nav-pills-success .show > .nav-link {
  background-color: #4caf50;
}

.nav-tabs-info .tab-content,
.nav-tabs-info .nav-tabs {
  border-color: #003473 !important;
}

.nav-tabs-info .nav-link.active {
  border-color: #003473 #003473 #fff !important;
  color: #003473 !important;
}

.nav-tabs-info .nav-link:hover {
  color: #003473;
}

.nav-pills-info .nav-link.active,
.nav-pills-info .show > .nav-link {
  background-color: #003473;
}

.nav-tabs-warning .tab-content,
.nav-tabs-warning .nav-tabs {
  border-color: #e97d23 !important;
}

.nav-tabs-warning .nav-link.active {
  border-color: #e97d23 #e97d23 #fff !important;
  color: #e97d23 !important;
}

.nav-tabs-warning .nav-link:hover {
  color: #e97d23;
}

.nav-pills-warning .nav-link.active,
.nav-pills-warning .show > .nav-link {
  background-color: #e97d23;
}

.nav-tabs-danger .tab-content,
.nav-tabs-danger .nav-tabs {
  border-color: #f44336 !important;
}

.nav-tabs-danger .nav-link.active {
  border-color: #f44336 #f44336 #fff !important;
  color: #f44336 !important;
}

.nav-tabs-danger .nav-link:hover {
  color: #f44336;
}

.nav-pills-danger .nav-link.active,
.nav-pills-danger .show > .nav-link {
  background-color: #f44336;
}

.nav-tabs-light .tab-content,
.nav-tabs-light .nav-tabs {
  border-color: #666666 !important;
}

.nav-tabs-light .nav-link.active {
  border-color: #666666 #666666 #fff !important;
  color: #666666 !important;
}

.nav-tabs-light .nav-link:hover {
  color: #666666;
}

.nav-pills-light .nav-link.active,
.nav-pills-light .show > .nav-link {
  background-color: #666666;
}

.nav-tabs-dark .tab-content,
.nav-tabs-dark .nav-tabs {
  border-color: #0a0a0a !important;
}

.nav-tabs-dark .nav-link.active {
  border-color: #0a0a0a #0a0a0a #fff !important;
  color: #0a0a0a !important;
}

.nav-tabs-dark .nav-link:hover {
  color: #0a0a0a;
}

.nav-pills-dark .nav-link.active,
.nav-pills-dark .show > .nav-link {
  background-color: #0a0a0a;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}
.nav-link.disabled {
  color: #525252;
}

.nav-tabs {
  border-bottom: 1px solid #999999;
}
.nav-tabs .nav-item {
  margin-bottom: -1px;
}
.nav-tabs .nav-link {
  border: 1px solid transparent;
  color: #525252;
}
.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  border-color: transparent;
}
.nav-tabs .nav-link.disabled {
  color: #525252;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  background-color: #fff;
}

.nav-pills {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.nav-pills .nav-link {
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(122, 122, 122, 0.6);
  margin: 0 0.5rem;
  color: #0a0a0a;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  box-shadow: none;
}

.tab-content {
  padding: 1rem;
  background-color: #fff;
}
[class^="nav-tabs-"] .tab-content,
[class*=" nav-tabs-"] .tab-content {
  border-top: 0 !important;
  border-right: 1px solid;
  border-bottom: 1px solid;
  border-left: 1px solid;
  border-bottom-left-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
[class^="nav-pills-"] .tab-content,
[class*=" nav-pills-"] .tab-content {
  border-radius: 0.25rem;
  box-shadow: 0 2px 12px 0 rgba(122, 122, 122, 0.6);
}

.navbar {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  transition: all 0.4s ease-in;
}
.navbar.main-navbar {
  padding-top: 24px;
}
.navbar.on-scroll {
  padding-top: 0.625rem !important;
}

.navbar-nav {
  font-size: 0.75rem;
}
.navbar-nav .nav-item .nav-link {
  text-transform: uppercase;
}
.navbar-nav .nav-item .nav-link i {
  font-size: 1rem;
  padding: 0 0.25rem;
}
.navbar-nav .nav-item .feather {
  height: 18px;
  width: 18px;
}

.navbar-dark .nav-item .nav-link {
  color: #fff;
}

.navbar-light .nav-item .nav-link {
  color: #1a1a1a;
}

.list-1 {
  border: 0;
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
}
.list-1 .list-group-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 1.875rem 2.5rem;
  border: 0;
  border-radius: 0 !important;
  border-left: 3px solid transparent;
  transition: all 0.2s ease-in-out !important;
}
@media (max-width: 991px) {
  .list-1 .list-group-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
.list-1 .list-group-item:hover {
  box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
  border-left: 3px solid #8b5cf6;
  z-index: 4;
}
.list-1 .list-group-item:hover .item-action .btn {
  background: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
  color: #fff;
}
.list-1 .list-group-item .item-thumbnail,
.list-1 .list-group-item .item-icon {
  height: 3.5rem;
  width: 3.5rem;
  border-radius: 0.25rem;
}
.list-1 .list-group-item .item-thumbnail {
  padding: 0.5rem;
}
@media (max-width: 991px) {
  .list-1 .list-group-item .item-thumbnail {
    padding: 0;
    margin-bottom: 1rem;
  }
}
.list-1 .list-group-item .item-icon {
  background: #f9f9f9;
  border: 1px solid #f9f9f9;
  color: #525252;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 991px) {
  .list-1 .list-group-item .item-icon {
    margin-bottom: 1rem;
  }
}
.list-1 .list-group-item .item-content {
  flex-grow: 1;
  padding: 0 2.5rem 0 0.9375rem;
}
@media (max-width: 991px) {
  .list-1 .list-group-item .item-content {
    padding: 0;
    margin-bottom: 1rem;
  }
}
.list-1 .list-group-item .item-content h4 {
  font-size: 1.125rem;
  margin-top: 0;
  margin-bottom: 0.375rem;
  font-weight: 400;
}
.list-1 .list-group-item .item-content .item-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
}
@media (max-width: 991px) {
  .list-1 .list-group-item .item-content .item-footer {
    flex-direction: column;
    align-items: flex-start;
  }
}
.list-1 .list-group-item .item-content .item-footer span {
  color: #525252;
  padding-right: 1.25rem;
  font-size: 0.8125rem;
}
.list-1 .list-group-item .item-content .item-footer span i,
.list-1 .list-group-item .item-content .item-footer span svg {
  margin-right: 0.375rem;
}
.list-1 .list-group-item .item-action .btn {
  background: #fff !important;
  border-color: #fff;
  color: #0a0a0a;
}
.list-1 .list-group-item .item-action .btn:focus {
  color: #0a0a0a !important;
}

.modal-content {
  border: 0;
}

.modal-backdrop.show {
  opacity: 0.2;
}

@media (min-width: 576px) {
  .modal-sm {
    max-width: 380px;
  }
  .modal-content {
    box-shadow: 0 4px 20px 0 rgba(122, 122, 122, 0.6);
  }
}

.pagination {
  border-radius: 0;
}

.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0;
  border-radius: 50% !important;
  color: #525252;
  height: 2.25rem;
  width: 2.25rem;
  padding: 0 !important;
  margin: 0 0.25rem;
}
.page-link:hover,
.page-link:focus {
  color: #525252;
  background-color: #f9f9f9;
  box-shadow: none;
}
.page-item.active .page-link {
  background-color: #8b5cf6;
  box-shadow: 0 2px 12px 0 rgba(102, 51, 153, 0.6);
}
.pagination-sm .page-link {
  height: 1.875rem;
  width: 1.875rem;
}
.pagination-lg .page-link {
  height: 2.5rem;
  width: 2.5rem;
}

.popover {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(122, 122, 122, 0.6);
}
.popover .arrow::before {
  border-color: rgba(0, 0, 0, 0);
}

.popover-primary {
  background-color: #8b5cf6;
  color: #fff;
}
.popover-primary .popover-body {
  color: #fff;
}
.popover-primary.bs-popover-top > .arrow::after {
  border-top-color: #8b5cf6;
}
.popover-primary.bs-popover-right > .arrow::after {
  border-right-color: #8b5cf6;
}
.popover-primary.bs-popover-bottom > .arrow::after {
  border-bottom-color: #8b5cf6;
}
.popover-primary.bs-popover-left > .arrow::after {
  border-left-color: #8b5cf6;
}

.popover-secondary {
  background-color: #1f1f1f;
  color: #fff;
}
.popover-secondary .popover-body {
  color: #fff;
}
.popover-secondary.bs-popover-top > .arrow::after {
  border-top-color: #1f1f1f;
}
.popover-secondary.bs-popover-right > .arrow::after {
  border-right-color: #1f1f1f;
}
.popover-secondary.bs-popover-bottom > .arrow::after {
  border-bottom-color: #1f1f1f;
}
.popover-secondary.bs-popover-left > .arrow::after {
  border-left-color: #1f1f1f;
}

.popover-success {
  background-color: #4caf50;
  color: #fff;
}
.popover-success .popover-body {
  color: #fff;
}
.popover-success.bs-popover-top > .arrow::after {
  border-top-color: #4caf50;
}
.popover-success.bs-popover-right > .arrow::after {
  border-right-color: #4caf50;
}
.popover-success.bs-popover-bottom > .arrow::after {
  border-bottom-color: #4caf50;
}
.popover-success.bs-popover-left > .arrow::after {
  border-left-color: #4caf50;
}

.popover-info {
  background-color: #003473;
  color: #fff;
}
.popover-info .popover-body {
  color: #fff;
}
.popover-info.bs-popover-top > .arrow::after {
  border-top-color: #003473;
}
.popover-info.bs-popover-right > .arrow::after {
  border-right-color: #003473;
}
.popover-info.bs-popover-bottom > .arrow::after {
  border-bottom-color: #003473;
}
.popover-info.bs-popover-left > .arrow::after {
  border-left-color: #003473;
}

.popover-warning {
  background-color: #e97d23;
  color: #fff;
}
.popover-warning .popover-body {
  color: #fff;
}
.popover-warning.bs-popover-top > .arrow::after {
  border-top-color: #e97d23;
}
.popover-warning.bs-popover-right > .arrow::after {
  border-right-color: #e97d23;
}
.popover-warning.bs-popover-bottom > .arrow::after {
  border-bottom-color: #e97d23;
}
.popover-warning.bs-popover-left > .arrow::after {
  border-left-color: #e97d23;
}

.popover-danger {
  background-color: #f44336;
  color: #fff;
}
.popover-danger .popover-body {
  color: #fff;
}
.popover-danger.bs-popover-top > .arrow::after {
  border-top-color: #f44336;
}
.popover-danger.bs-popover-right > .arrow::after {
  border-right-color: #f44336;
}
.popover-danger.bs-popover-bottom > .arrow::after {
  border-bottom-color: #f44336;
}
.popover-danger.bs-popover-left > .arrow::after {
  border-left-color: #f44336;
}

.popover-light {
  background-color: #666666;
  color: #fff;
}
.popover-light .popover-body {
  color: #fff;
}
.popover-light.bs-popover-top > .arrow::after {
  border-top-color: #666666;
}
.popover-light.bs-popover-right > .arrow::after {
  border-right-color: #666666;
}
.popover-light.bs-popover-bottom > .arrow::after {
  border-bottom-color: #666666;
}
.popover-light.bs-popover-left > .arrow::after {
  border-left-color: #666666;
}

.popover-dark {
  background-color: #0a0a0a;
  color: #fff;
}
.popover-dark .popover-body {
  color: #fff;
}
.popover-dark.bs-popover-top > .arrow::after {
  border-top-color: #0a0a0a;
}
.popover-dark.bs-popover-right > .arrow::after {
  border-right-color: #0a0a0a;
}
.popover-dark.bs-popover-bottom > .arrow::after {
  border-bottom-color: #0a0a0a;
}
.popover-dark.bs-popover-left > .arrow::after {
  border-left-color: #0a0a0a;
}

.progress-wrapper {
  padding-top: 1rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
}

.progress-percentage {
  font-size: 0.8125rem;
  color: #525252;
  font-weight: 500;
}

.progress {
  height: 0.375rem;
  background-color: #999999;
  border-radius: 1rem;
  box-shadow: none;
}

.progress-bar {
  background-color: #8b5cf6;
}

.pricing1 {
  text-align: center;
  padding: 1.5rem;
  border-radius: 1rem;
  max-width: 280px;
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease-in-out !important;
}
.pricing1 .card-img {
  width: auto;
  max-height: 10rem;
}
.pricing1 .heading {
  color: #4d2673;
  font-weight: 300;
  letter-spacing: 0.1875rem;
}
.pricing1:hover:hover {
  transform: scale(1.1);
}
.pricing1 .price {
  font-size: 3rem;
  font-weight: 500;
  color: #8b5cf6;
  line-height: 1.25;
}
.pricing1 .price + small {
  display: inline-block;
  color: #525252;
  margin-bottom: 1.5rem;
  margin-top: -0.25rem;
}

.bootstrap-select.show > .btn {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.12) !important;
}

.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  top: 9px;
}
.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark::after {
  width: 7px;
  height: 14px;
  border-width: 0 2px 2px 0;
}

.bootstrap-select .dropdown-menu {
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.12) !important;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin: 0;
}

.bootstrap-select .dropdown-toggle:focus {
  outline: none !important;
}

.slider .tooltip.in {
  opacity: 1;
}

.slider .tooltip .tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #000;
}

.slider.slider-horizontal .slider-track {
  height: 4px;
  width: 100%;
  margin-top: -2px;
  background: #999999;
  box-shadow: none;
}
.slider.slider-horizontal .slider-track .slider-selection {
  box-shadow: none;
  background: #8b5cf6;
}

.slider .slider-handle {
  background: #8b5cf6;
  box-shadow: 0 2px 12px 0 rgba(102, 51, 153, 0.6);
  transition: box-shadow 0.2s ease-in-out;
}
.slider .slider-handle:hover {
  box-shadow: 0 4px 20px 0 rgba(102, 51, 153, 0.6);
}

.section {
  padding: 80px 0;
  position: relative;
}

.section-header {
  min-height: 100vh;
  padding-top: 120px;
  height: 750px;
}
.section-header .section-header-image {
  position: absolute;
  height: 100%;
  top: 0;
  right: 0;
  z-index: -1;
}
.section-header .section-header-content {
  padding-top: 3rem;
}

.demo-components .btn {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.header-container {
  z-index: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  min-height: 100vh;
  background-blend-mode: soft-light;
  background-size: cover;
  background-position: center center;
}

.bird {
  background-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/174479/bird-cells.svg);
  background-size: auto 100%;
  width: 88px;
  height: 125px;
  will-change: background-position;
  -webkit-animation-name: fly-cycle;
  animation-name: fly-cycle;
  -webkit-animation-timing-function: steps(10);
  animation-timing-function: steps(10);
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.bird--one {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}
.bird--two {
  -webkit-animation-duration: 0.9s;
  animation-duration: 0.9s;
  -webkit-animation-delay: -0.75s;
  animation-delay: -0.75s;
}
.bird--three {
  -webkit-animation-duration: 1.25s;
  animation-duration: 1.25s;
  -webkit-animation-delay: -0.25s;
  animation-delay: -0.25s;
}
.bird--four {
  -webkit-animation-duration: 1.1s;
  animation-duration: 1.1s;
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}

.bird-container {
  position: absolute;
  top: 20%;
  left: -10%;
  transform: scale(0) translateX(-10vw);
  will-change: transform;
  -webkit-animation-name: fly-right-one;
  animation-name: fly-right-one;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.bird-container--one {
  -webkit-animation-duration: 15s;
  animation-duration: 15s;
  -webkit-animation-delay: 0;
  animation-delay: 0;
}
.bird-container--two {
  -webkit-animation-duration: 16s;
  animation-duration: 16s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.bird-container--three {
  -webkit-animation-duration: 14.6s;
  animation-duration: 14.6s;
  -webkit-animation-delay: 9.5s;
  animation-delay: 9.5s;
}
.bird-container--four {
  -webkit-animation-duration: 16s;
  animation-duration: 16s;
  -webkit-animation-delay: 10.25s;
  animation-delay: 10.25s;
}

@-webkit-keyframes fly-cycle {
  100% {
    background-position: -900px 0;
  }
}

@keyframes fly-cycle {
  100% {
    background-position: -900px 0;
  }
}

@-webkit-keyframes fly-right-one {
  0% {
    transform: scale(0.3) translateX(-10vw);
  }
  10% {
    transform: translateY(-15vh) translateX(10vw) scale(0.4);
  }
  20% {
    transform: translateY(-10vh) translateX(30vw) scale(0.5);
  }
  30% {
    transform: translateY(-10vh) translateX(50vw) scale(0.6);
  }
  40% {
    transform: translateY(2vh) translateX(70vw) scale(0.6);
  }
  50% {
    transform: translateY(0vh) translateX(90vw) scale(0.6);
  }
  60% {
    transform: translateY(0vh) translateX(110vw) scale(0.6);
  }
  100% {
    transform: translateY(0vh) translateX(110vw) scale(0.6);
  }
}

@keyframes fly-right-one {
  0% {
    transform: scale(0.3) translateX(-10vw);
  }
  10% {
    transform: translateY(-15vh) translateX(10vw) scale(0.4);
  }
  20% {
    transform: translateY(-10vh) translateX(30vw) scale(0.5);
  }
  30% {
    transform: translateY(-10vh) translateX(50vw) scale(0.6);
  }
  40% {
    transform: translateY(2vh) translateX(70vw) scale(0.6);
  }
  50% {
    transform: translateY(0vh) translateX(90vw) scale(0.6);
  }
  60% {
    transform: translateY(0vh) translateX(110vw) scale(0.6);
  }
  100% {
    transform: translateY(0vh) translateX(110vw) scale(0.6);
  }
}

@-webkit-keyframes fly-right-two {
  0% {
    transform: translateY(-2vh) translateX(-10vw) scale(0.5);
  }
  10% {
    transform: translateY(0vh) translateX(10vw) scale(0.4);
  }
  20% {
    transform: translateY(-4vh) translateX(30vw) scale(0.6);
  }
  30% {
    transform: translateY(1vh) translateX(50vw) scale(0.45);
  }
  40% {
    transform: translateY(-2.5vh) translateX(70vw) scale(0.5);
  }
  50% {
    transform: translateY(0vh) translateX(90vw) scale(0.45);
  }
  51% {
    transform: translateY(0vh) translateX(110vw) scale(0.45);
  }
  100% {
    transform: translateY(0vh) translateX(110vw) scale(0.45);
  }
}

@keyframes fly-right-two {
  0% {
    transform: translateY(-2vh) translateX(-10vw) scale(0.5);
  }
  10% {
    transform: translateY(0vh) translateX(10vw) scale(0.4);
  }
  20% {
    transform: translateY(-4vh) translateX(30vw) scale(0.6);
  }
  30% {
    transform: translateY(1vh) translateX(50vw) scale(0.45);
  }
  40% {
    transform: translateY(-2.5vh) translateX(70vw) scale(0.5);
  }
  50% {
    transform: translateY(0vh) translateX(90vw) scale(0.45);
  }
  51% {
    transform: translateY(0vh) translateX(110vw) scale(0.45);
  }
  100% {
    transform: translateY(0vh) translateX(110vw) scale(0.45);
  }
}

/*


  Code viewer
*/
.copy-code {
  position: relative;
}
.copy-code .btn-clipboard {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.2rem 0.4rem;
}

.code {
  margin-bottom: 1rem;
}

.hljs {
  background: #f9f9f9;
}

.doc-example {
  padding: 1.5rem;
  border: 3px solid #f9f9f9;
  border-bottom: none;
}

.doc-section-title {
  margin: 1.5rem 0 1rem 0;
  font-weight: 500;
}
.doc-section-title::before {
  display: block;
  height: 6rem;
  margin-top: -6rem;
  visibility: hidden;
  content: "";
}
.doc-section-title:hover .section-link {
  opacity: 1;
}
.doc-section-title .section-link {
  opacity: 0;
  padding-left: 0.3rem;
}
.doc-section-title .section-link::before {
  font-family: "Font Awesome 5 Free";
  content: "\f0c1";
  font-weight: 900 !important;
  font-size: 18px;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  vertical-align: middle;
}

/*
  Layout
*/
.doc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 3.75rem;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  box-shadow: 0 2px 12px 0 rgba(122, 122, 122, 0.6);
  z-index: 101;
  padding: 10px 20px;
  background: #fff;
}
.doc-header .brand a {
  display: block;
  font-size: 1.125rem;
  font-weight: 500;
  color: black;
  line-height: 1.1;
  letter-spacing: 2px;
}
.doc-header .brand a:hover {
  text-decoration: none;
}
.doc-header .sidebar-toggle {
  display: none;
}
@media (max-width: 767px) {
  .doc-header .sidebar-toggle {
    display: inline-block;
  }
}
.doc-header .sidebar-toggle .ti-close {
  display: none;
}
.doc-header .sidebar-toggle .ti-menu {
  display: inline-block;
}
.sidebar-open .doc-header .sidebar-toggle .ti-close {
  display: inline-block;
}
.sidebar-open .doc-header .sidebar-toggle .ti-menu {
  display: none;
}

.doc-content__sidebar {
  width: 240px;
  position: fixed;
  height: calc(100vh - 3.75rem);
  top: 3.75rem;
  left: 0;
  border-right: 1px solid #999999;
  background: rgba(249, 249, 249, 0.5);
  overflow-y: scroll;
  z-index: 100;
  background: #fff;
}
.sidebar-open .doc-content__sidebar {
  left: 0 !important;
}
@media (max-width: 767px) {
  .doc-content__sidebar {
    left: -240px;
  }
}
.doc-content__sidebar .sidebar__list {
  list-style: none;
  text-transform: capitalize;
  margin: 0;
  padding: 30px 0 0 0;
}
.doc-content__sidebar .sidebar__list > li > a {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}
.doc-content__sidebar .sidebar__list li a {
  display: block;
  color: #666666;
  padding: 4px 20px;
}
.doc-content__sidebar .sidebar__list li a:hover {
  text-decoration: none;
}
.doc-content__sidebar .sidebar__list li ul {
  list-style: none;
  text-transform: capitalize;
  margin: 0 0 1.5rem;
  padding: 0;
}
.doc-content__sidebar .sidebar__list li ul li a {
  color: #0a0a0a;
  border-radius: 0 30px 30px 0;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 0.875rem;
  padding-top: 4px;
  padding-bottom: 4px;
  margin: 2px 8px 2px 0;
}
.doc-content__sidebar .sidebar__list li ul li a.active {
  background: #8b5cf6;
  color: #fff;
}
.doc-content__sidebar .sidebar__list li ul li a.active:hover {
  background: #8b5cf6;
}
.doc-content__sidebar .sidebar__list li ul li a:hover {
  background: #999999;
}

.doc-content__body {
  position: relative;
  padding: 5rem 1.5rem 1.5rem;
  width: calc(100% - 70px - 200px);
  left: 240px;
  z-index: 96;
}
@media (max-width: 767px) {
  .doc-content__body {
    width: 100%;
    left: 0;
  }
}

.doc-content__secondary-sidebar {
  position: fixed;
  width: 200px;
  top: 3.75rem;
  right: 0;
  border-left: 1px solid #999999;
  margin-top: 2rem;
  padding: 0.25rem 1.5rem;
}
@media (max-width: 767px) {
  .doc-content__secondary-sidebar {
    display: none;
  }
}
.doc-content__secondary-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.doc-content__secondary-sidebar ul li a {
  display: inline-block;
  font-size: 0.875rem;
  color: #666666;
  padding: 0.125rem 0;
  font-weight: 500;
}
.doc-content__secondary-sidebar ul li a:hover {
  color: #8b5cf6;
  text-decoration: none;
}
.doc-content__secondary-sidebar ul li ul {
  padding-left: 1rem;
}
.doc-content__secondary-sidebar ul li ul a {
  font-size: 0.8125rem;
}

@media (max-width: 767px) {
  .sidebar-container .sidebar {
    background: #fff;
  }
  .sidebar-container .sidebar .sidebar-close {
    display: block;
  }
}
